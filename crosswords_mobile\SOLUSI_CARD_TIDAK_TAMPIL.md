# 🔧 Solusi: Card Tidak Tampil di Halaman Teka-Teki-Silang

## 🎯 Masalah
Card crossword tidak muncul di halaman teka-teki-silang aplikasi mobile Flutter.

## ⚡ UPDATE: Masalah Diperbaiki

### 1. JSON Parsing Error ✅ FIXED
**Error**: `TypeError: Instance of 'JSArray<dynamic>': type 'List<dynamic>' is not a subtype of type 'Map<String, dynamic>'`

**Penyebab**: Error terjadi di `ApiResponse.fromJson` ketika mem-parsing data dengan generic types.

**Solusi**:
- Diperbaiki di `lib/core/services/api_service.dart` dengan manual parsing
- Diperbaiki di `lib/core/models/crossword_models.dart` dengan try-catch handling
- Menambahkan logging detail untuk debugging

### 2. Navigation/Routing Error
**Error**: `Navigator.onGenerateRoute was null, but the route named "/crossword/{id}/play/" was referenced.`

**Penyebab**: Ketidakcocokan path route dan penggunaan Navigator.pushNamed dengan GoRouter.

**Solusi**: <PERSON><PERSON><PERSON><PERSON> di `crossword_detail_screen.dart` dan `crosswords_list_screen.dart` menggunakan AppRouter helper methods.

### 3. Konfirmasi Format Response Server ✅
**Response server**: Menggunakan format standard `{status, data, pagination}` seperti yang diharapkan.

**Struktur data crossword**: Sudah sesuai dengan model Flutter, termasuk grid_data dengan format `{char, wordIds}`.

## 🚀 Solusi Cepat (3 Langkah)

### Langkah 1: Cari IP Address Komputer Anda

**Windows:**
```cmd
ipconfig
```
Cari "IPv4 Address", contoh: `*************`

**macOS/Linux:**
```bash
ifconfig | grep "inet "
```

### Langkah 2: Update Konfigurasi API

Edit file `lib/core/config/app_config.dart`:

```dart
// SEBELUM (SALAH)
static const String baseUrl = 'http://localhost:1111/api';

// SESUDAH (BENAR)
static const String baseUrl = 'http://*************:1111/api'; // Ganti dengan IP Anda
```

### Langkah 3: Restart Server API

```bash
cd ../crosswords-api
php -S 0.0.0.0:1111 -t public
```

**Penting:** Gunakan `0.0.0.0:1111` bukan `localhost:1111`

## 🛠️ Solusi Otomatis

Jalankan script yang sudah disediakan:

```bash
# Windows
scripts\fix_cards_issue.bat

# macOS/Linux
chmod +x scripts/fix_cards_issue.sh
./scripts/fix_cards_issue.sh
```

## ✅ Verifikasi

1. Buka aplikasi mobile
2. Navigasi ke "Teka-Teki Silang"
3. Card harus muncul
4. Jika ada error, akan muncul Debug Info Widget

## 🔍 Debugging

Jika masih bermasalah, perhatikan log di Flutter console:

- 🔄 Loading crosswords
- 🌐 API Request
- 📡 API Response
- ❌ API Error

## 📱 Test Manual

Test API di browser:
```
http://*************:1111/api/crosswords
```

Harus mengembalikan JSON dengan format:
```json
{
  "status": "success",
  "data": [...]
}
```

## ⚠️ Catatan Penting

- **JANGAN** gunakan `localhost` atau `127.0.0.1` untuk mobile
- **GUNAKAN** IP address yang sebenarnya
- **PASTIKAN** server binding ke `0.0.0.0`
- **CEK** firewall tidak memblokir port 1111

## 🆘 Jika Masih Bermasalah

1. Pastikan komputer dan mobile di WiFi yang sama
2. Cek firewall/antivirus
3. Coba IP address lain (192.168.0.x, 10.0.0.x)
4. Restart router WiFi
5. Lihat log error di Flutter console
