import 'package:flutter/material.dart';
import 'lib/providers/crossword_game_provider.dart';
import 'lib/core/models/crossword_models.dart';

/// Simple test to verify highlighting functionality
void main() {
  print('🧪 Testing Cell Highlighting Functionality');
  print('=' * 50);
  
  testHighlightingLogic();
}

void testHighlightingLogic() {
  print('\n🔍 Testing highlighting logic...');
  
  // Create a simple test crossword
  final testCrossword = Crossword(
    id: 'test-1',
    title: 'Test Crossword',
    slug: 'test-crossword',
    gridSize: 5,
    gridData: [
      [
        GridCell(char: 'H', wordIds: [1, 2]),
        GridCell(char: 'E', wordIds: [1]),
        GridCell(char: 'L', wordIds: [1]),
        GridCell(char: 'L', wordIds: [1]),
        GridCell(char: 'O', wordIds: [1]),
      ],
      [
        GridCell(char: 'A', wordIds: [2]),
        GridCell(char: ' ', wordIds: []),
        <PERSON>ridCell(char: ' ', wordIds: []),
        <PERSON>rid<PERSON>ell(char: ' ', wordIds: []),
        Grid<PERSON>ell(char: ' ', wordIds: []),
      ],
      [
        GridCell(char: 'P', wordIds: [2]),
        GridCell(char: ' ', wordIds: []),
        GridCell(char: ' ', wordIds: []),
        GridCell(char: ' ', wordIds: []),
        GridCell(char: ' ', wordIds: []),
      ],
      [
        GridCell(char: 'P', wordIds: [2]),
        GridCell(char: ' ', wordIds: []),
        GridCell(char: ' ', wordIds: []),
        GridCell(char: ' ', wordIds: []),
        GridCell(char: ' ', wordIds: []),
      ],
      [
        GridCell(char: 'Y', wordIds: [2]),
        GridCell(char: ' ', wordIds: []),
        GridCell(char: ' ', wordIds: []),
        GridCell(char: ' ', wordIds: []),
        GridCell(char: ' ', wordIds: []),
      ],
    ],
    words: ['HELLO', 'HAPPY'],
    clues: CrosswordClues(
      across: {'1': 'Greeting'},
      down: {'2': 'Feeling good'},
    ),
    wordPositions: [
      WordPosition(
        number: 1,
        word: 'HELLO',
        row: 0,
        col: 0,
        direction: 'across',
        isAcross: true,
        isDown: false,
      ),
      WordPosition(
        number: 2,
        word: 'HAPPY',
        row: 0,
        col: 0,
        direction: 'down',
        isAcross: false,
        isDown: true,
      ),
    ],
    difficulty: 'easy',
    plays: 0,
    rating: 0.0,
    categoryName: 'Test',
    creator: 'Test Creator',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
  
  // Create game provider
  final gameProvider = CrosswordGameProvider();
  
  try {
    print('\n📋 Test 1: Initialize Game');
    gameProvider.initializeGame(testCrossword);
    print('   ✅ Game initialized successfully');
    
    print('\n📋 Test 2: Select Cell (0,0) - Should highlight HELLO across');
    gameProvider.selectCell(0, 0, preferredDirection: Direction.across);
    
    print('   Selected cell: ${gameProvider.selectedCell}');
    print('   Highlighted cells count: ${gameProvider.highlightedCells.length}');
    
    for (int i = 0; i < gameProvider.highlightedCells.length; i++) {
      final cell = gameProvider.highlightedCells[i];
      print('   Highlighted cell $i: (${cell.row}, ${cell.col})');
    }
    
    if (gameProvider.highlightedCells.length == 5) {
      print('   ✅ HELLO word highlighted correctly (5 cells)');
    } else {
      print('   ❌ Expected 5 highlighted cells, got ${gameProvider.highlightedCells.length}');
    }
    
    print('\n📋 Test 3: Toggle Direction - Should highlight HAPPY down');
    gameProvider.selectCell(0, 0, preferredDirection: Direction.down);
    
    print('   Selected cell: ${gameProvider.selectedCell}');
    print('   Highlighted cells count: ${gameProvider.highlightedCells.length}');
    
    for (int i = 0; i < gameProvider.highlightedCells.length; i++) {
      final cell = gameProvider.highlightedCells[i];
      print('   Highlighted cell $i: (${cell.row}, ${cell.col})');
    }
    
    if (gameProvider.highlightedCells.length == 5) {
      print('   ✅ HAPPY word highlighted correctly (5 cells)');
    } else {
      print('   ❌ Expected 5 highlighted cells, got ${gameProvider.highlightedCells.length}');
    }
    
    print('\n📋 Test 4: Select Different Cell (0,2) - Should highlight HELLO');
    gameProvider.selectCell(0, 2);
    
    print('   Selected cell: ${gameProvider.selectedCell}');
    print('   Highlighted cells count: ${gameProvider.highlightedCells.length}');
    
    if (gameProvider.highlightedCells.length == 5) {
      print('   ✅ HELLO word highlighted correctly from middle cell');
    } else {
      print('   ❌ Expected 5 highlighted cells, got ${gameProvider.highlightedCells.length}');
    }
    
    print('\n🎉 All highlighting tests completed!');
    
  } catch (e, stackTrace) {
    print('\n❌ Test failed: $e');
    print('Stack trace: $stackTrace');
  }
}
