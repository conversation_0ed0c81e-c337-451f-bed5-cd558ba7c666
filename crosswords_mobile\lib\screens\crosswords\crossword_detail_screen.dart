import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/models/crossword_models.dart';
import '../../providers/crossword_provider.dart';
import '../../widgets/crosswords/crossword_grid.dart';
import '../../core/router/app_router.dart';

class CrosswordDetailScreen extends StatefulWidget {
  final String crosswordId;

  const CrosswordDetailScreen({
    super.key,
    required this.crosswordId,
  });

  @override
  State<CrosswordDetailScreen> createState() => _CrosswordDetailScreenState();
}

class _CrosswordDetailScreenState extends State<CrosswordDetailScreen> {
  bool _isLoading = true;
  Crossword? _crossword;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadCrossword();
  }

  Future<void> _loadCrossword() async {
    try {
      final crosswordProvider = context.read<CrosswordProvider>();
      final crossword =
          await crosswordProvider.getCrossword(widget.crosswordId);

      if (mounted) {
        setState(() {
          _crossword = crossword;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Memuat...'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null || _crossword == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                _error ?? 'Teka-teki silang tidak ditemukan',
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Kembali'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_crossword!.title),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Basic Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _crossword!.title,
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                    ),
                    const SizedBox(height: 8),
                    Text('Kategori: ${_crossword!.categoryName ?? 'Umum'}'),
                    Text('Kesulitan: ${_crossword!.difficulty}'),
                    Text(
                        'Grid: ${_crossword!.gridSize}x${_crossword!.gridSize}'),
                    Text('Dimainkan: ${_crossword!.plays} kali'),
                    if (_crossword!.rating != null)
                      Text('Rating: ${_crossword!.rating!.toStringAsFixed(1)}'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Preview
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Preview Grid',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 16),
                    Center(
                      child: CrosswordGridPreview(
                        crossword: _crossword!,
                        cellSize: 20,
                        showUserInput: false,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Description
            if (_crossword!.description != null &&
                _crossword!.description!.isNotEmpty)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Deskripsi',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: 8),
                      Text(_crossword!.description!),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // Action Buttons
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _startGame,
                icon: const Icon(Icons.play_arrow),
                label: const Text('Mulai Bermain'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _startGame() {
    AppRouter.pushCrosswordPlay(context, widget.crosswordId);
  }
}
