import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/router/app_router.dart';
import '../../providers/auth_provider.dart';
import '../../providers/crossword_provider.dart';
import '../../widgets/common/app_drawer.dart';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final isScrolled = _scrollController.offset > 100;
    if (isScrolled != _isScrolled) {
      setState(() {
        _isScrolled = isScrolled;
      });
    }
  }

  Future<void> _loadData() async {
    final crosswordProvider = context.read<CrosswordProvider>();
    await Future.wait([
      crosswordProvider.loadFeaturedCrosswords(),
      crosswordProvider.loadCategories(),
    ]);
  }

  Future<void> _onRefresh() async {
    final crosswordProvider = context.read<CrosswordProvider>();
    await crosswordProvider.refreshAll();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authProvider = context.watch<AuthProvider>();

    return Scaffold(
      key: _scaffoldKey,
      drawer: const AppDrawer(),
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: _isScrolled 
                ? theme.colorScheme.surface.withOpacity(0.95)
                : Colors.transparent,
            elevation: _isScrolled ? 1 : 0,
            leading: IconButton(
              icon: const Icon(Icons.menu),
              onPressed: () => _scaffoldKey.currentState?.openDrawer(),
            ),
            actions: [
              if (!authProvider.isAuthenticated)
                TextButton(
                  onPressed: () => AppRouter.pushLogin(context),
                  child: const Text('Masuk'),
                ),
              if (authProvider.isAuthenticated)
                IconButton(
                  icon: CircleAvatar(
                    radius: 16,
                    backgroundImage: authProvider.avatarUrl != null
                        ? NetworkImage(authProvider.avatarUrl!)
                        : null,
                    child: authProvider.avatarUrl == null
                        ? Text(
                            authProvider.displayName[0].toUpperCase(),
                            style: const TextStyle(fontSize: 14),
                          )
                        : null,
                  ),
                  onPressed: () => AppRouter.goToProfile(context),
                ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      theme.colorScheme.primary.withOpacity(0.8),
                      theme.colorScheme.primary.withOpacity(0.6),
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(height: 40), // Space for app bar
                        Icon(
                          Icons.grid_3x3,
                          size: 48,
                          color: theme.colorScheme.onPrimary,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'tekateki.io',
                          style: theme.textTheme.headlineMedium?.copyWith(
                            color: theme.colorScheme.onPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Asah Otak dengan Teka-Teki Silang',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onPrimary.withOpacity(0.9),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: RefreshIndicator(
              onRefresh: _onRefresh,
              child: Column(
                children: [
                  // Quick Actions
                  const QuickActionsSection(),
                  
                  // Stats Section
                  const StatsSection(),
                  
                  // Featured Crosswords
                  const FeaturedCrosswordsSection(),
                  
                  // Categories
                  const CategoriesSection(),
                  
                  // Bottom spacing
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => AppRouter.goToCrosswords(context),
        icon: const Icon(Icons.play_arrow),
        label: const Text('Mulai Bermain'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
    );
  }
}
