import 'dart:convert';
import '../lib/core/models/crossword_models.dart';

/// Script untuk menguji robust parsing yang telah diperbaiki
/// Jalankan dengan: dart run scripts/test_robust_parsing.dart
void main() {
  print('🧪 Testing Robust JSON Parsing');
  print('=' * 50);
  
  testRobustParsing();
  testEdgeCases();
  
  print('\n✅ Test selesai!');
}

void testRobustParsing() {
  print('\n🔍 Testing robust parsing dengan data server yang sebenarnya...');
  
  // Data server yang sebenarnya dari user
  final realServerData = {
    "id": "dae3a1e6-b176-446d-a7ea-8e4c34abb1e5",
    "title": "Teka teki Edit",
    "slug": "teka-teki-edit",
    "description": "",
    "grid_size": 5,
    "grid_data": [
      [
        {"char": " ", "wordIds": []},
        {"char": " ", "wordIds": []},
        {"char": " ", "wordIds": []},
        {"char": " ", "wordIds": []},
        {"char": " ", "wordIds": []}
      ],
      [
        {"char": " ", "wordIds": []},
        {"char": "T", "wordIds": [2]},
        {"char": " ", "wordIds": []},
        {"char": " ", "wordIds": []},
        {"char": " ", "wordIds": []}
      ],
      [
        {"char": "T", "wordIds": [1]},
        {"char": "E", "wordIds": [1, 2]},
        {"char": "K", "wordIds": [1]},
        {"char": "A", "wordIds": [1]},
        {"char": " ", "wordIds": []}
      ],
      [
        {"char": " ", "wordIds": []},
        {"char": "K", "wordIds": [2]},
        {"char": " ", "wordIds": []},
        {"char": " ", "wordIds": []},
        {"char": " ", "wordIds": []}
      ],
      [
        {"char": " ", "wordIds": []},
        {"char": "I", "wordIds": [2, 3]},
        {"char": "O", "wordIds": [3]},
        {"char": " ", "wordIds": []},
        {"char": " ", "wordIds": []}
      ]
    ],
    "words": ["TEKA", "TEKI", "IO"],
    "clues": {
      "down": {"2": "teki"},
      "across": {"1": "teka", "3": "io"}
    },
    "word_positions": [
      {
        "col": 0,
        "row": 2,
        "word": "TEKA",
        "number": 1,
        "direction": "across"
      },
      {
        "col": 1,
        "row": 1,
        "word": "TEKI",
        "number": 2,
        "direction": "down"
      },
      {
        "col": 1,
        "row": 4,
        "word": "IO",
        "number": 3,
        "direction": "across"
      }
    ],
    "difficulty": "mudah",
    "user_id": "5b4dc128-8ba5-4bd3-b990-6de4ddd11522",
    "is_public": 1,
    "plays": 15,
    "rating": null,
    "rating_count": 0,
    "category_id": "c001a2b4-6d7e-11ee-8c99-0242ac120002",
    "created_at": "2025-05-25T09:58:46.000000Z",
    "updated_at": "2025-05-25T11:20:05.000000Z",
    "category_name": "Pengetahuan Umum",
    "category_slug": "pengetahuan-umum",
    "creator": "widi yanata"
  };
  
  try {
    print('\n📋 Test 1: Parsing Real Server Data');
    final crossword = Crossword.fromJson(realServerData);
    
    print('   ✅ ID: ${crossword.id}');
    print('   ✅ Title: ${crossword.title}');
    print('   ✅ Grid Size: ${crossword.gridSize}');
    print('   ✅ Grid Data: ${crossword.gridData.length}x${crossword.gridData[0].length}');
    print('   ✅ Words: ${crossword.words}');
    print('   ✅ Clues Across: ${crossword.clues.across.keys.length}');
    print('   ✅ Clues Down: ${crossword.clues.down.keys.length}');
    print('   ✅ Word Positions: ${crossword.wordPositions.length}');
    print('   ✅ Difficulty: ${crossword.difficulty}');
    print('   ✅ Plays: ${crossword.plays}');
    print('   ✅ Category: ${crossword.categoryName}');
    
    // Test grid cells
    print('\n📋 Test 2: Grid Cell Parsing');
    for (int row = 0; row < crossword.gridData.length; row++) {
      String rowStr = '';
      for (int col = 0; col < crossword.gridData[row].length; col++) {
        final cell = crossword.gridData[row][col];
        rowStr += cell.char == ' ' ? '·' : cell.char;
      }
      print('   Row $row: $rowStr');
    }
    
    print('\n🎉 Real server data parsing: SUCCESS!');
    
  } catch (e, stackTrace) {
    print('\n❌ Real server data parsing failed: $e');
    print('Stack trace: $stackTrace');
  }
}

void testEdgeCases() {
  print('\n🔍 Testing edge cases...');
  
  // Test 1: Minimal data
  print('\n📋 Test 1: Minimal Data');
  try {
    final minimalData = {
      "id": "test-id",
      "title": "Test Crossword",
      "slug": "test-crossword",
      "grid_size": 3,
      "difficulty": "mudah",
      "user_id": "test-user",
      "is_public": 1,
      "plays": 0,
      "rating_count": 0,
      "created_at": "2025-01-01T00:00:00.000000Z",
      "updated_at": "2025-01-01T00:00:00.000000Z"
    };
    
    final crossword = Crossword.fromJson(minimalData);
    print('   ✅ Minimal data: ${crossword.title}');
    print('   ✅ Grid created: ${crossword.gridData.length}x${crossword.gridData[0].length}');
  } catch (e) {
    print('   ❌ Minimal data failed: $e');
  }
  
  // Test 2: Malformed grid_data
  print('\n📋 Test 2: Malformed Grid Data');
  try {
    final malformedData = {
      "id": "test-id-2",
      "title": "Test Malformed",
      "slug": "test-malformed",
      "grid_size": 3,
      "grid_data": "invalid_data", // String instead of array
      "difficulty": "mudah",
      "user_id": "test-user",
      "is_public": 1,
      "plays": 0,
      "rating_count": 0,
      "created_at": "2025-01-01T00:00:00.000000Z",
      "updated_at": "2025-01-01T00:00:00.000000Z"
    };
    
    final crossword = Crossword.fromJson(malformedData);
    print('   ✅ Malformed data handled: ${crossword.title}');
    print('   ✅ Fallback grid created: ${crossword.gridData.length}x${crossword.gridData[0].length}');
  } catch (e) {
    print('   ❌ Malformed data failed: $e');
  }
  
  // Test 3: Null/missing fields
  print('\n📋 Test 3: Null/Missing Fields');
  try {
    final nullData = {
      "id": "test-id-3",
      "title": "Test Null",
      "slug": "test-null",
      "grid_size": 3,
      "grid_data": null,
      "words": null,
      "clues": null,
      "word_positions": null,
      "difficulty": "mudah",
      "user_id": "test-user",
      "is_public": 1,
      "plays": 0,
      "rating_count": 0,
      "created_at": "2025-01-01T00:00:00.000000Z",
      "updated_at": "2025-01-01T00:00:00.000000Z"
    };
    
    final crossword = Crossword.fromJson(nullData);
    print('   ✅ Null fields handled: ${crossword.title}');
    print('   ✅ Empty arrays created: words=${crossword.words.length}, positions=${crossword.wordPositions.length}');
  } catch (e) {
    print('   ❌ Null fields failed: $e');
  }
}
