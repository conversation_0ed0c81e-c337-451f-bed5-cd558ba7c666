import 'dart:io';
import 'dart:convert';

/// Script untuk membantu setup konfigurasi API
/// Jalankan dengan: dart run scripts/setup_api_config.dart
void main() async {
  print('🔧 Setup Konfigurasi API untuk Mobile Development');
  print('=' * 50);
  
  // 1. Deteksi IP address
  await detectIPAddress();
  
  // 2. Test koneksi ke server
  await testServerConnection();
  
  // 3. Update konfigurasi
  await updateApiConfig();
  
  print('\n✅ Setup selesai!');
  print('📱 Sekarang coba jalankan aplikasi mobile');
}

Future<void> detectIPAddress() async {
  print('\n🔍 Mendeteksi IP Address...');
  
  try {
    // Mendapatkan semua network interfaces
    final interfaces = await NetworkInterface.list();
    
    print('\n📡 Network Interfaces yang tersedia:');
    for (final interface in interfaces) {
      for (final addr in interface.addresses) {
        if (addr.type == InternetAddressType.IPv4 && !addr.isLoopback) {
          print('   ${interface.name}: ${addr.address}');
        }
      }
    }
    
    // Cari IP address yang paling mungkin (biasanya WiFi)
    String? recommendedIP;
    for (final interface in interfaces) {
      if (interface.name.toLowerCase().contains('wi-fi') || 
          interface.name.toLowerCase().contains('wlan') ||
          interface.name.toLowerCase().contains('en0')) {
        for (final addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4 && !addr.isLoopback) {
            recommendedIP = addr.address;
            break;
          }
        }
      }
    }
    
    if (recommendedIP != null) {
      print('\n💡 IP Address yang direkomendasikan: $recommendedIP');
      print('   Gunakan IP ini untuk konfigurasi API');
    }
    
  } catch (e) {
    print('❌ Error mendeteksi IP: $e');
  }
}

Future<void> testServerConnection() async {
  print('\n🧪 Testing koneksi ke server...');
  
  // Daftar IP yang mungkin
  final possibleIPs = [
    '*************',
    '*************', 
    '*************',
    '*************',
    '**********',
    'localhost'
  ];
  
  for (final ip in possibleIPs) {
    final url = 'http://$ip:1111/api/crosswords';
    print('   Testing: $url');
    
    try {
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 3);
      
      final request = await client.getUrl(Uri.parse(url));
      final response = await request.close();
      
      if (response.statusCode == 200) {
        final responseBody = await response.transform(utf8.decoder).join();
        print('   ✅ Berhasil! Status: ${response.statusCode}');
        
        // Parse response untuk cek format
        try {
          final jsonData = jsonDecode(responseBody);
          if (jsonData['status'] == 'success') {
            print('   📊 Data format: OK');
            print('   📈 Jumlah data: ${jsonData['data']?.length ?? 0}');
          }
        } catch (e) {
          print('   ⚠️  Response bukan JSON valid');
        }
        
        print('   🎯 Gunakan IP ini: $ip');
        break;
      } else {
        print('   ❌ Status: ${response.statusCode}');
      }
      
      client.close();
    } catch (e) {
      print('   ❌ Gagal: ${e.toString().split(':').first}');
    }
  }
}

Future<void> updateApiConfig() async {
  print('\n⚙️  Update konfigurasi API...');
  
  try {
    final configFile = File('lib/core/config/app_config.dart');
    
    if (!await configFile.exists()) {
      print('❌ File konfigurasi tidak ditemukan');
      return;
    }
    
    final content = await configFile.readAsString();
    
    // Cek apakah masih menggunakan localhost
    if (content.contains('localhost:1111')) {
      print('⚠️  Masih menggunakan localhost!');
      print('📝 Silakan update manual di lib/core/config/app_config.dart');
      print('   Ganti: http://localhost:1111/api');
      print('   Dengan: http://IP_ADDRESS_ANDA:1111/api');
    } else {
      print('✅ Konfigurasi sudah menggunakan IP address');
    }
    
  } catch (e) {
    print('❌ Error membaca konfigurasi: $e');
  }
}
