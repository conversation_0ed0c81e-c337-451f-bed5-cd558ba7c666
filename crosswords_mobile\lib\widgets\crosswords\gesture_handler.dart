import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../providers/crossword_game_provider.dart';

/// Widget that handles touch gestures for crossword game
class GestureHandler extends StatefulWidget {
  final Widget child;
  final bool enableZoom;
  final bool enableSwipeNavigation;

  const GestureHandler({
    super.key,
    required this.child,
    this.enableZoom = true,
    this.enableSwipeNavigation = true,
  });

  @override
  State<GestureHandler> createState() => _GestureHandlerState();
}

class _GestureHandlerState extends State<GestureHandler>
    with TickerProviderStateMixin {
  late TransformationController _transformationController;
  late AnimationController _animationController;
  Animation<Matrix4>? _animation;

  // Zoom constraints
  static const double _minScale = 0.5;
  static const double _maxScale = 3.0;
  static const double _defaultScale = 1.0;

  // Swipe detection
  static const double _swipeThreshold = 50.0;
  static const double _swipeVelocityThreshold = 300.0;

  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enableZoom && !widget.enableSwipeNavigation) {
      return widget.child;
    }

    Widget child = widget.child;

    // Add zoom functionality
    if (widget.enableZoom) {
      child = InteractiveViewer(
        transformationController: _transformationController,
        minScale: _minScale,
        maxScale: _maxScale,
        constrained: false,
        child: child,
      );
    }

    // Add swipe navigation
    if (widget.enableSwipeNavigation) {
      child = GestureDetector(
        onPanEnd: _handlePanEnd,
        behavior: HitTestBehavior.translucent, // Allow taps to pass through
        child: child,
      );
    }

    return child;
  }

  void _handlePanEnd(DragEndDetails details) {
    final velocity = details.velocity.pixelsPerSecond;
    final gameProvider = context.read<CrosswordGameProvider>();

    // Check if velocity is high enough for swipe
    if (velocity.distance < _swipeVelocityThreshold) return;

    // Determine swipe direction
    final dx = velocity.dx;
    final dy = velocity.dy;

    if (dx.abs() > dy.abs()) {
      // Horizontal swipe
      if (dx > 0) {
        // Swipe right
        gameProvider.moveRight();
        _provideFeedback();
      } else {
        // Swipe left
        gameProvider.moveLeft();
        _provideFeedback();
      }
    } else {
      // Vertical swipe
      if (dy > 0) {
        // Swipe down
        gameProvider.moveDown();
        _provideFeedback();
      } else {
        // Swipe up
        gameProvider.moveUp();
        _provideFeedback();
      }
    }
  }

  void _provideFeedback() {
    final gameProvider = context.read<CrosswordGameProvider>();
    if (gameProvider.vibrationEnabled) {
      HapticFeedback.lightImpact();
    }
  }

  /// Reset zoom to default scale
  void resetZoom() {
    _animateToScale(_defaultScale);
  }

  /// Zoom in
  void zoomIn() {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    final newScale = (currentScale * 1.5).clamp(_minScale, _maxScale);
    _animateToScale(newScale);
  }

  /// Zoom out
  void zoomOut() {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    final newScale = (currentScale / 1.5).clamp(_minScale, _maxScale);
    _animateToScale(newScale);
  }

  /// Animate to specific scale
  void _animateToScale(double scale) {
    final currentTransform = _transformationController.value;
    final targetTransform = Matrix4.identity()..scale(scale);

    _animation = Matrix4Tween(
      begin: currentTransform,
      end: targetTransform,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.reset();
    _animationController.forward().then((_) {
      _transformationController.value = targetTransform;
    });

    _animation!.addListener(() {
      _transformationController.value = _animation!.value;
    });
  }

  /// Focus on specific cell by centering it
  void focusOnCell(int row, int col, Size gridSize, Size cellSize) {
    final screenSize = MediaQuery.of(context).size;

    // Calculate cell position
    final cellX = col * cellSize.width;
    final cellY = row * cellSize.height;

    // Calculate center offset
    final centerX = screenSize.width / 2 - cellSize.width / 2;
    final centerY = screenSize.height / 2 - cellSize.height / 2;

    // Calculate translation to center the cell
    final translateX = centerX - cellX;
    final translateY = centerY - cellY;

    final targetTransform = Matrix4.identity()
      ..translate(translateX, translateY);

    _animateToTransform(targetTransform);
  }

  /// Animate to specific transform
  void _animateToTransform(Matrix4 targetTransform) {
    final currentTransform = _transformationController.value;

    _animation = Matrix4Tween(
      begin: currentTransform,
      end: targetTransform,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.reset();
    _animationController.forward().then((_) {
      _transformationController.value = targetTransform;
    });

    _animation!.addListener(() {
      _transformationController.value = _animation!.value;
    });
  }
}

/// Zoom control buttons widget
class ZoomControls extends StatelessWidget {
  final VoidCallback? onZoomIn;
  final VoidCallback? onZoomOut;
  final VoidCallback? onReset;

  const ZoomControls({
    super.key,
    this.onZoomIn,
    this.onZoomOut,
    this.onReset,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildZoomButton(
            context,
            icon: Icons.zoom_in,
            onPressed: onZoomIn,
            tooltip: 'Perbesar',
          ),
          const Divider(height: 1),
          _buildZoomButton(
            context,
            icon: Icons.zoom_out,
            onPressed: onZoomOut,
            tooltip: 'Perkecil',
          ),
          const Divider(height: 1),
          _buildZoomButton(
            context,
            icon: Icons.center_focus_strong,
            onPressed: onReset,
            tooltip: 'Reset Zoom',
          ),
        ],
      ),
    );
  }

  Widget _buildZoomButton(
    BuildContext context, {
    required IconData icon,
    required VoidCallback? onPressed,
    required String tooltip,
  }) {
    final theme = Theme.of(context);

    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          child: Icon(
            icon,
            size: 20,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
    );
  }
}
