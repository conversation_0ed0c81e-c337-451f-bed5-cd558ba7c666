import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Paper-inspired color scheme (matching web version)
  static const Color paperPrimary = Color(0xFFFFFFFF);
  static const Color paperSecondary = Color(0xFFF5F5F5);
  static const Color paperMedium = Color(0xFFF0F0F0);
  static const Color paperSubtle = Color(0xFFFAFAFA);
  
  static const Color inkPrimary = Color(0xFF171717);
  static const Color inkSecondary = Color(0xFF404040);
  static const Color inkMuted = Color(0xFF737373);
  
  // Dark theme colors
  static const Color darkPaperPrimary = Color(0xFF262626);
  static const Color darkPaperSecondary = Color(0xFF404040);
  static const Color darkInkPrimary = Color(0xFFF5F5F5);
  static const Color darkInkSecondary = Color(0xFFD4D4D4);
  static const Color darkInkMuted = Color(0xFFA3A3A3);
  
  // Accent colors
  static const Color accentBlue = Color(0xFF3B82F6);
  static const Color accentGreen = Color(0xFF10B981);
  static const Color accentRed = Color(0xFFEF4444);
  static const Color accentYellow = Color(0xFFF59E0B);
  
  // Typography
  static TextTheme get _textTheme => GoogleFonts.robotoTextTheme().copyWith(
    displayLarge: GoogleFonts.roboto(
      fontSize: 32,
      fontWeight: FontWeight.bold,
      color: inkPrimary,
    ),
    displayMedium: GoogleFonts.roboto(
      fontSize: 28,
      fontWeight: FontWeight.bold,
      color: inkPrimary,
    ),
    displaySmall: GoogleFonts.roboto(
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: inkPrimary,
    ),
    headlineLarge: GoogleFonts.roboto(
      fontSize: 22,
      fontWeight: FontWeight.w600,
      color: inkPrimary,
    ),
    headlineMedium: GoogleFonts.roboto(
      fontSize: 20,
      fontWeight: FontWeight.w600,
      color: inkPrimary,
    ),
    headlineSmall: GoogleFonts.roboto(
      fontSize: 18,
      fontWeight: FontWeight.w600,
      color: inkPrimary,
    ),
    titleLarge: GoogleFonts.roboto(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: inkPrimary,
    ),
    titleMedium: GoogleFonts.roboto(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: inkPrimary,
    ),
    titleSmall: GoogleFonts.roboto(
      fontSize: 12,
      fontWeight: FontWeight.w500,
      color: inkPrimary,
    ),
    bodyLarge: GoogleFonts.roboto(
      fontSize: 16,
      fontWeight: FontWeight.normal,
      color: inkSecondary,
    ),
    bodyMedium: GoogleFonts.roboto(
      fontSize: 14,
      fontWeight: FontWeight.normal,
      color: inkSecondary,
    ),
    bodySmall: GoogleFonts.roboto(
      fontSize: 12,
      fontWeight: FontWeight.normal,
      color: inkMuted,
    ),
  );

  static TextTheme get _darkTextTheme => _textTheme.apply(
    bodyColor: darkInkSecondary,
    displayColor: darkInkPrimary,
  );

  // Light Theme
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    colorScheme: const ColorScheme.light(
      primary: inkPrimary,
      secondary: accentBlue,
      surface: paperPrimary,
      background: paperSubtle,
      error: accentRed,
      onPrimary: paperPrimary,
      onSecondary: paperPrimary,
      onSurface: inkPrimary,
      onBackground: inkPrimary,
      onError: paperPrimary,
    ),
    textTheme: _textTheme,
    scaffoldBackgroundColor: paperSubtle,
    appBarTheme: AppBarTheme(
      backgroundColor: paperPrimary,
      foregroundColor: inkPrimary,
      elevation: 1,
      shadowColor: Colors.black.withOpacity(0.1),
      titleTextStyle: GoogleFonts.roboto(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: inkPrimary,
      ),
    ),
    cardTheme: CardTheme(
      color: paperPrimary,
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.black.withOpacity(0.1),
          width: 1,
        ),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: inkPrimary,
        foregroundColor: paperPrimary,
        elevation: 2,
        shadowColor: Colors.black.withOpacity(0.2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w600,
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: inkPrimary,
        side: const BorderSide(color: inkPrimary, width: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w600,
        ),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: paperPrimary,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.black.withOpacity(0.2)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.black.withOpacity(0.2)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: accentBlue, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: accentRed, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    ),
  );

  // Dark Theme
  static ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: const ColorScheme.dark(
      primary: darkInkPrimary,
      secondary: accentBlue,
      surface: darkPaperPrimary,
      background: Color(0xFF171717),
      error: accentRed,
      onPrimary: darkPaperPrimary,
      onSecondary: darkPaperPrimary,
      onSurface: darkInkPrimary,
      onBackground: darkInkPrimary,
      onError: darkPaperPrimary,
    ),
    textTheme: _darkTextTheme,
    scaffoldBackgroundColor: const Color(0xFF171717),
    appBarTheme: AppBarTheme(
      backgroundColor: darkPaperPrimary,
      foregroundColor: darkInkPrimary,
      elevation: 1,
      shadowColor: Colors.black.withOpacity(0.3),
      titleTextStyle: GoogleFonts.roboto(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: darkInkPrimary,
      ),
    ),
    cardTheme: CardTheme(
      color: darkPaperPrimary,
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: darkInkPrimary,
        foregroundColor: darkPaperPrimary,
        elevation: 2,
        shadowColor: Colors.black.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w600,
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: darkInkPrimary,
        side: const BorderSide(color: darkInkPrimary, width: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: GoogleFonts.roboto(
          fontWeight: FontWeight.w600,
        ),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: darkPaperPrimary,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.white.withOpacity(0.2)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.white.withOpacity(0.2)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: accentBlue, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: accentRed, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    ),
  );
}
