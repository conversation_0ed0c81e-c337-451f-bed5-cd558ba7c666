import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/models/crossword_models.dart';
import '../../providers/crossword_game_provider.dart';

class CrosswordClues extends StatefulWidget {
  final Crossword crossword;
  final bool showOnlySelected;
  final ScrollController? scrollController;

  const CrosswordClues({
    super.key,
    required this.crossword,
    this.showOnlySelected = false,
    this.scrollController,
  });

  @override
  State<CrosswordClues> createState() => _CrosswordCluesState();
}

class _CrosswordCluesState extends State<CrosswordClues>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _acrossScrollController;
  late ScrollController _downScrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _acrossScrollController = ScrollController();
    _downScrollController = ScrollController();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _acrossScrollController.dispose();
    _downScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final gameProvider = context.watch<CrosswordGameProvider>();

    if (widget.showOnlySelected) {
      return _buildSelectedClueWidget(theme, gameProvider);
    }

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Handle bar for dragging
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Tab bar
          TabBar(
            controller: _tabController,
            labelColor: theme.colorScheme.primary,
            unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.6),
            indicatorColor: theme.colorScheme.primary,
            tabs: [
              Tab(
                text: 'Mendatar (${widget.crossword.clues.across.length})',
              ),
              Tab(
                text: 'Menurun (${widget.crossword.clues.down.length})',
              ),
            ],
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCluesList(
                  widget.crossword.clues.across,
                  Direction.across,
                  _acrossScrollController,
                  gameProvider,
                ),
                _buildCluesList(
                  widget.crossword.clues.down,
                  Direction.down,
                  _downScrollController,
                  gameProvider,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedClueWidget(
      ThemeData theme, CrosswordGameProvider gameProvider) {
    final selectedCell = gameProvider.selectedCell;

    if (selectedCell == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          'Pilih kotak untuk melihat petunjuk',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    final directionText =
        selectedCell.direction == Direction.across ? 'Mendatar' : 'Menurun';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$directionText ${selectedCell.wordNumber}',
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.swap_horiz),
                onPressed: () => _switchDirection(gameProvider),
                tooltip: 'Ganti arah',
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            selectedCell.clue,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCluesList(
    Map<String, String> clues,
    Direction direction,
    ScrollController scrollController,
    CrosswordGameProvider gameProvider,
  ) {
    final sortedEntries = clues.entries.toList()
      ..sort((a, b) => int.parse(a.key).compareTo(int.parse(b.key)));

    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: sortedEntries.length,
      itemBuilder: (context, index) {
        final entry = sortedEntries[index];
        final number = int.parse(entry.key);
        final clue = entry.value;

        final isSelected = gameProvider.selectedCell?.wordNumber == number &&
            gameProvider.selectedCell?.direction == direction;

        return _ClueItem(
          number: number,
          clue: clue,
          direction: direction,
          isSelected: isSelected,
          onTap: () => _onClueTap(gameProvider, number, direction),
        );
      },
    );
  }

  void _onClueTap(
      CrosswordGameProvider gameProvider, int number, Direction direction) {
    // Find the starting position of this word
    final crossword = gameProvider.crossword;
    if (crossword == null) return;

    debugPrint('_onClueTap: looking for word $number in direction $direction');

    // Method 1: Try using wordPositions (preferred)
    bool foundFromPositions =
        _selectWordFromPositions(gameProvider, crossword, number, direction);

    if (!foundFromPositions) {
      // Method 2: Fallback - find using grid wordIds
      debugPrint('_onClueTap: falling back to grid-based word finding');
      _selectWordFromGrid(gameProvider, crossword, number, direction);
    }
  }

  // Method 1: Select word using wordPositions array
  bool _selectWordFromPositions(CrosswordGameProvider gameProvider,
      Crossword crossword, int number, Direction direction) {
    for (final wordPos in crossword.wordPositions) {
      if (wordPos.number == number) {
        if ((direction == Direction.across && wordPos.isAcross) ||
            (direction == Direction.down && wordPos.isDown)) {
          debugPrint(
              '_selectWordFromPositions: found word at (${wordPos.row}, ${wordPos.col})');
          gameProvider.selectCell(
            wordPos.row,
            wordPos.col,
            preferredDirection: direction,
          );
          return true;
        }
      }
    }
    return false;
  }

  // Method 2: Select word using grid wordIds (fallback)
  void _selectWordFromGrid(CrosswordGameProvider gameProvider,
      Crossword crossword, int number, Direction direction) {
    // Find any cell that belongs to this word
    for (int row = 0; row < crossword.gridSize; row++) {
      for (int col = 0; col < crossword.gridSize; col++) {
        final cell = crossword.gridData[row][col];
        if (cell.wordIds.contains(number)) {
          // Found a cell belonging to this word
          debugPrint(
              '_selectWordFromGrid: found word $number at cell ($row, $col)');

          // Try to find the start of the word by reconstructing it
          final wordInfo =
              gameProvider.reconstructWordFromGrid(row, col, number, direction);
          if (wordInfo != null &&
              wordInfo.containsKey('startRow') &&
              wordInfo.containsKey('startCol')) {
            final startRow = wordInfo['startRow'] as int;
            final startCol = wordInfo['startCol'] as int;

            debugPrint(
                '_selectWordFromGrid: selecting word start at ($startRow, $startCol)');
            gameProvider.selectCell(
              startRow,
              startCol,
              preferredDirection: direction,
            );
            return;
          } else {
            // If we can't find the start, just select this cell
            debugPrint(
                '_selectWordFromGrid: could not find word start, selecting current cell');
            gameProvider.selectCell(
              row,
              col,
              preferredDirection: direction,
            );
            return;
          }
        }
      }
    }

    debugPrint('_selectWordFromGrid: word $number not found in grid');
  }

  void _switchDirection(CrosswordGameProvider gameProvider) {
    final selectedCell = gameProvider.selectedCell;
    if (selectedCell == null) return;

    final newDirection = selectedCell.direction == Direction.across
        ? Direction.down
        : Direction.across;

    gameProvider.selectCell(
      selectedCell.row,
      selectedCell.col,
      preferredDirection: newDirection,
    );
  }
}

class _ClueItem extends StatelessWidget {
  final int number;
  final String clue;
  final Direction direction;
  final bool isSelected;
  final VoidCallback onTap;

  const _ClueItem({
    required this.number,
    required this.clue,
    required this.direction,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: isSelected
            ? theme.colorScheme.primary.withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      number.toString(),
                      style: theme.textTheme.labelMedium?.copyWith(
                        color: isSelected
                            ? theme.colorScheme.onPrimary
                            : theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    clue,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Compact clues widget for mobile landscape mode
class CompactCrosswordClues extends StatelessWidget {
  final Crossword crossword;
  final double maxHeight;

  const CompactCrosswordClues({
    super.key,
    required this.crossword,
    this.maxHeight = 120,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final gameProvider = context.watch<CrosswordGameProvider>();
    final selectedCell = gameProvider.selectedCell;

    if (selectedCell == null) {
      return const SizedBox.shrink();
    }

    return Container(
      constraints: BoxConstraints(maxHeight: maxHeight),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${selectedCell.direction == Direction.across ? 'Mendatar' : 'Menurun'} ${selectedCell.wordNumber}',
              style: theme.textTheme.labelMedium?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              selectedCell.clue,
              style: theme.textTheme.bodySmall,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
