import 'package:flutter/foundation.dart';

import '../core/models/crossword_models.dart';
import '../core/services/api_service.dart';
import '../core/services/storage_service.dart';
import '../core/config/app_config.dart';

class CrosswordProvider extends ChangeNotifier {
  final ApiService _apiService;
  final StorageService _storageService = StorageService();

  CrosswordProvider(this._apiService);

  // State variables
  List<Crossword> _crosswords = [];
  List<CrosswordCategory> _categories = [];
  List<Crossword> _featuredCrosswords = [];
  List<Progress> _userProgress = [];

  bool _isLoading = false;
  bool _isLoadingMore = false;
  String? _error;

  int _currentPage = 1;
  bool _hasMoreData = true;
  String? _currentCategory;
  String? _currentDifficulty;
  String? _currentSearch;

  // Getters
  List<Crossword> get crosswords => _crosswords;
  List<CrosswordCategory> get categories => _categories;
  List<Crossword> get featuredCrosswords => _featuredCrosswords;
  List<Progress> get userProgress => _userProgress;

  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  String? get error => _error;
  bool get hasMoreData => _hasMoreData;

  // Load crosswords with filters
  Future<void> loadCrosswords({
    String? category,
    String? difficulty,
    String? search,
    bool refresh = false,
  }) async {
    print(
        '🔄 Loading crosswords - category: $category, difficulty: $difficulty, search: $search, refresh: $refresh');

    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _crosswords.clear();
    }

    // Prevent multiple simultaneous requests
    if (_isLoading || _isLoadingMore || !_hasMoreData) {
      print('⏸️ Skipping load - already loading or no more data');
      return;
    }

    try {
      // Set loading state
      if (_currentPage == 1) {
        _isLoading = true;
      } else {
        _isLoadingMore = true;
      }
      _error = null;

      // Update current filters
      _currentCategory = category;
      _currentDifficulty = difficulty;
      _currentSearch = search;

      // Notify once at the beginning
      notifyListeners();

      // Try to load from cache first (only for first page)
      if (_currentPage == 1) {
        final cacheKey =
            'crosswords_${category ?? 'all'}_${difficulty ?? 'all'}_${search ?? 'all'}';
        final cachedData = await _storageService.getCachedData<List<Crossword>>(
          cacheKey,
          (data) =>
              (data as List).map((item) => Crossword.fromJson(item)).toList(),
          maxAge: const Duration(minutes: 30),
        );

        if (cachedData != null && cachedData.isNotEmpty) {
          _crosswords = cachedData;
          _isLoading = false;
          // Don't notify here, will notify at the end
        }
      }

      final response = await _apiService.getCrosswords(
        category: category,
        difficulty: difficulty,
        search: search,
        page: _currentPage,
        limit: AppConfig.defaultPageSize,
      );

      print(
          '📡 API Response - status: ${response.status}, message: ${response.message}');
      print('📊 Data length: ${response.data?.length ?? 0}');

      if (response.isSuccess && response.data != null) {
        final newCrosswords = response.data!;

        if (_currentPage == 1) {
          _crosswords = newCrosswords;

          // Cache the data
          final cacheKey =
              'crosswords_${category ?? 'all'}_${difficulty ?? 'all'}_${search ?? 'all'}';
          await _storageService.cacheData(
              cacheKey, newCrosswords.map((c) => c.toJson()).toList());
        } else {
          _crosswords.addAll(newCrosswords);
        }

        _hasMoreData = newCrosswords.length >= AppConfig.defaultPageSize;
        _currentPage++;
        print('✅ Successfully loaded ${newCrosswords.length} crosswords');
      } else {
        _error = response.message;
        print('❌ API Error: ${response.message}');
      }
    } on ApiException catch (e) {
      _error = e.message;
      print('🚨 API Exception: ${e.message}');
    } catch (e) {
      _error = 'Terjadi kesalahan saat memuat teka-teki silang';
      print('💥 Unexpected error: $e');
    } finally {
      _isLoading = false;
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  // Load more crosswords (pagination)
  Future<void> loadMoreCrosswords() async {
    if (!_hasMoreData || _isLoadingMore) return;

    await loadCrosswords(
      category: _currentCategory,
      difficulty: _currentDifficulty,
      search: _currentSearch,
    );
  }

  // Load featured crosswords
  Future<void> loadFeaturedCrosswords() async {
    try {
      // Try cache first
      final cachedData = await _storageService.getCachedData<List<Crossword>>(
        'featured_crosswords',
        (data) =>
            (data as List).map((item) => Crossword.fromJson(item)).toList(),
        maxAge: const Duration(hours: 1),
      );

      if (cachedData != null) {
        _featuredCrosswords = cachedData;
        notifyListeners();
      }

      final response = await _apiService.getFeaturedCrosswords();

      if (response.isSuccess && response.data != null) {
        _featuredCrosswords = response.data!;

        // Cache the data
        await _storageService.cacheData(
          'featured_crosswords',
          _featuredCrosswords.map((c) => c.toJson()).toList(),
        );

        notifyListeners();
      }
    } catch (e) {
      // Silently handle errors for featured content
    }
  }

  // Load categories
  Future<void> loadCategories() async {
    try {
      // Try cache first
      final cachedData =
          await _storageService.getCachedData<List<CrosswordCategory>>(
        'categories',
        (data) => (data as List)
            .map((item) => CrosswordCategory.fromJson(item))
            .toList(),
        maxAge: const Duration(hours: 6),
      );

      if (cachedData != null) {
        _categories = cachedData;
        notifyListeners();
      }

      final response = await _apiService.getCategories();

      if (response.isSuccess && response.data != null) {
        _categories = response.data!;

        // Cache the data
        await _storageService.cacheData(
          'categories',
          _categories.map((c) => c.toJson()).toList(),
        );

        notifyListeners();
      }
    } catch (e) {
      // Silently handle errors for categories
    }
  }

  // Get single crossword
  Future<Crossword?> getCrossword(String id) async {
    try {
      // Check if already in memory
      final existingCrossword = _crosswords.firstWhere(
        (c) => c.id == id,
        orElse: () => _featuredCrosswords.firstWhere(
          (c) => c.id == id,
          orElse: () => throw StateError('Not found'),
        ),
      );

      return existingCrossword;
    } catch (e) {
      // Not found in memory, continue to API call
    }

    try {
      // Try cache first
      final cachedData = await _storageService.getCachedData<Crossword>(
        'crossword_$id',
        (data) => Crossword.fromJson(data),
        maxAge: const Duration(hours: 2),
      );

      if (cachedData != null) {
        return cachedData;
      }

      final response = await _apiService.getCrossword(id);

      if (response.isSuccess && response.data != null) {
        final crossword = response.data!;

        // Cache the data
        await _storageService.cacheData('crossword_$id', crossword.toJson());

        return crossword;
      }
    } catch (e) {
      // Handle error
    }

    return null;
  }

  // Record play
  Future<bool> recordPlay(String crosswordId) async {
    try {
      final response = await _apiService.recordPlay(crosswordId);
      return response.isSuccess;
    } catch (e) {
      return false;
    }
  }

  // Load user progress
  Future<void> loadUserProgress() async {
    try {
      final response = await _apiService.getProgress();

      if (response.isSuccess && response.data != null) {
        _userProgress = response.data!;
        notifyListeners();
      }
    } catch (e) {
      // Handle error silently
    }
  }

  // Get progress for specific crossword
  Future<Progress?> getCrosswordProgress(String crosswordId) async {
    try {
      // Check local storage first
      final localProgress =
          await _storageService.getCrosswordProgress(crosswordId);
      if (localProgress != null) {
        return localProgress;
      }

      // Try API
      final response = await _apiService.getCrosswordProgress(crosswordId);
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
    } catch (e) {
      // Handle error
    }

    return null;
  }

  // Save progress
  Future<bool> saveProgress(String crosswordId, Progress progress) async {
    try {
      // Save locally first
      await _storageService.setCrosswordProgress(crosswordId, progress);

      // Try to sync with server
      final response =
          await _apiService.saveProgress(crosswordId, progress.toJson());
      return response.isSuccess;
    } catch (e) {
      // Even if server sync fails, local save succeeded
      return true;
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Refresh all data
  Future<void> refreshAll() async {
    await Future.wait([
      loadCrosswords(refresh: true),
      loadFeaturedCrosswords(),
      loadCategories(),
    ]);
  }

  // Search crosswords
  Future<void> searchCrosswords(String query) async {
    await loadCrosswords(search: query, refresh: true);
  }

  // Filter by category
  Future<void> filterByCategory(String? category) async {
    await loadCrosswords(category: category, refresh: true);
  }

  // Filter by difficulty
  Future<void> filterByDifficulty(String? difficulty) async {
    await loadCrosswords(difficulty: difficulty, refresh: true);
  }
}
