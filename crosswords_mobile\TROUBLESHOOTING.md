# Troubleshooting - Card Tidak Tampil di Halaman Teka-Teki-Silang

## Masalah
Card crossword tidak tampil di halaman teka-teki-silang aplikasi mobile Flutter.

## Kemungkinan Penyebab & Solusi

### 1. Masalah Koneksi API (Paling Umum)

**Penyebab**: Mobile device tidak bisa mengakses `localhost:1111` karena localhost merujuk ke device itu sendiri, bukan ke komputer host.

**Solusi**:
1. **Ganti URL API di `lib/core/config/app_config.dart`**:
   ```dart
   // Ganti localhost dengan IP address komputer Anda
   static const String baseUrl = 'http://*************:1111/api';
   ```

2. **Cara mendapatkan IP address komputer**:
   - **Windows**: Buka Command Prompt, ketik `ipconfig`, cari "IPv4 Address"
   - **macOS/Linux**: Buka Terminal, ketik `ifconfig` atau `ip addr show`
   - **Contoh**: `*************`, `*************`, `*********`

3. **Pastikan server API berjalan**:
   ```bash
   # Di direktori crosswords-api
   php -S 0.0.0.0:1111 -t public
   ```
   **Penting**: Gunakan `0.0.0.0:1111` bukan `localhost:1111` agar bisa diakses dari device lain.

### 2. Masalah CORS (Cross-Origin Resource Sharing)

**Solusi**: Pastikan CORS dikonfigurasi dengan benar di backend PHP untuk menerima request dari mobile app.

### 3. Masalah Firewall

**Solusi**:
- Pastikan port 1111 tidak diblokir oleh firewall
- Untuk Windows: Windows Defender Firewall > Allow an app > PHP
- Untuk macOS: System Preferences > Security & Privacy > Firewall

### 4. Masalah Network

**Solusi**:
- Pastikan komputer dan mobile device terhubung ke WiFi yang sama
- Coba ping dari device ke komputer: `ping *************`

## Debugging Steps

### 1. Cek Log di Flutter
Jalankan aplikasi dengan debug mode dan perhatikan log di console:
```bash
flutter run
```

Cari log dengan emoji:
- 🔄 Loading crosswords
- 🌐 API Request
- 📡 API Response
- ❌ API Error
- 🚨 API Exception

### 2. Test API Manual
Test API endpoint dengan browser atau Postman:
```
GET http://*************:1111/api/crosswords
```

### 3. Cek Response API
Pastikan API mengembalikan response dengan format:
```json
{
  "status": "success",
  "message": "Data retrieved successfully",
  "data": [
    {
      "id": "...",
      "title": "...",
      "slug": "...",
      // ... data crossword lainnya
    }
  ]
}
```

## Quick Fix Commands

### 1. Jalankan Script Otomatis
```bash
# Windows
scripts\fix_cards_issue.bat

# macOS/Linux
chmod +x scripts/fix_cards_issue.sh
./scripts/fix_cards_issue.sh

# Atau langsung dengan Dart
dart run scripts/setup_api_config.dart
```

### 2. Update IP Address Manual
```bash
# Ganti IP_ADDRESS dengan IP komputer Anda
sed -i 's/localhost:1111/IP_ADDRESS:1111/g' lib/core/config/app_config.dart
```

### 3. Restart Server dengan Binding ke Semua Interface
```bash
cd ../  # ke direktori crosswords-api
php -S 0.0.0.0:1111 -t public
```

### 4. Test Koneksi
```bash
# Dari terminal/command prompt
curl http://IP_ADDRESS:1111/api/crosswords
```

## Verifikasi Solusi

1. **Buka aplikasi mobile**
2. **Navigasi ke halaman "Teka-Teki Silang"**
3. **Perhatikan log di console Flutter**
4. **Card crossword harus muncul jika koneksi berhasil**

## Jika Masih Bermasalah

1. **Cek log error di console Flutter**
2. **Cek log server PHP**
3. **Pastikan data crossword ada di database**
4. **Test API endpoint langsung di browser**

## Catatan Penting

- **Jangan gunakan `localhost` atau `127.0.0.1`** untuk mobile development
- **Gunakan IP address yang sebenarnya** dari komputer host
- **Pastikan server binding ke `0.0.0.0`** bukan hanya `localhost`
- **Firewall dan antivirus** bisa memblokir koneksi
