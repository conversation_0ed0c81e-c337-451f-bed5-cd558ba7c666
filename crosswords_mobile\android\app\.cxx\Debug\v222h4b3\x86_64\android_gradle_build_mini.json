{"buildFiles": ["D:\\belajar\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\crosswords-api\\crosswords_mobile\\android\\app\\.cxx\\Debug\\v222h4b3\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\crosswords-api\\crosswords_mobile\\android\\app\\.cxx\\Debug\\v222h4b3\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}