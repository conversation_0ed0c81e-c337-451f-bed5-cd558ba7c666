import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/crossword_game_provider.dart';

/// Game control buttons for crossword play screen
class GameControls extends StatelessWidget {
  final VoidCallback? onPause;
  final VoidCallback? onSettings;
  final VoidCallback? onHint;

  const GameControls({
    super.key,
    this.onPause,
    this.onSettings,
    this.onHint,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final gameProvider = context.watch<CrosswordGameProvider>();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.95),
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back),
            tooltip: 'Kembali',
          ),

          const SizedBox(width: 8),

          // Game title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  gameProvider.crossword?.title ?? 'Teka-Teki Silang',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (gameProvider.gameState == GameState.playing)
                  Text(
                    _formatTime(gameProvider.timeSpent),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
              ],
            ),
          ),

          // Control buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Hint button
              IconButton(
                onPressed: gameProvider.gameState == GameState.playing ? onHint : null,
                icon: const Icon(Icons.lightbulb_outline),
                tooltip: 'Petunjuk',
              ),

              // Pause/Resume button
              IconButton(
                onPressed: _getPauseResumeAction(gameProvider),
                icon: Icon(
                  gameProvider.gameState == GameState.paused
                      ? Icons.play_arrow
                      : Icons.pause,
                ),
                tooltip: gameProvider.gameState == GameState.paused
                    ? 'Lanjutkan'
                    : 'Jeda',
              ),

              // Settings button
              IconButton(
                onPressed: onSettings,
                icon: const Icon(Icons.settings),
                tooltip: 'Pengaturan',
              ),
            ],
          ),
        ],
      ),
    );
  }

  VoidCallback? _getPauseResumeAction(CrosswordGameProvider gameProvider) {
    if (gameProvider.gameState == GameState.playing) {
      return () => gameProvider.pauseGame();
    } else if (gameProvider.gameState == GameState.paused) {
      return () => gameProvider.resumeGame();
    }
    return null;
  }

  String _formatTime(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

/// Progress indicator for crossword completion
class GameProgress extends StatelessWidget {
  const GameProgress({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final gameProvider = context.watch<CrosswordGameProvider>();

    final progress = gameProvider.progressPercentage / 100;
    final completedCells = gameProvider.completedCells;
    final totalCells = gameProvider.totalCells;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progress',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '$completedCells / $totalCells',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: theme.colorScheme.surfaceContainerHighest,
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${(progress * 100).toStringAsFixed(1)}% selesai',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }
}

/// Hint dialog for showing clues or revealing letters
class HintDialog extends StatelessWidget {
  const HintDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final gameProvider = context.watch<CrosswordGameProvider>();

    return AlertDialog(
      title: const Text('Petunjuk'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (gameProvider.selectedCell != null) ...[
            Text(
              'Kata yang dipilih:',
              style: theme.textTheme.titleSmall,
            ),
            const SizedBox(height: 8),
            Text(
              gameProvider.selectedCell!.clue,
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
          ],
          
          // Hint options
          ListTile(
            leading: const Icon(Icons.lightbulb),
            title: const Text('Tampilkan huruf'),
            subtitle: const Text('Isi huruf pada kotak yang dipilih'),
            onTap: () {
              gameProvider.revealCurrentCell();
              Navigator.of(context).pop();
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.lightbulb_outline),
            title: const Text('Tampilkan kata'),
            subtitle: const Text('Isi seluruh kata yang dipilih'),
            onTap: () {
              gameProvider.revealCurrentWord();
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Batal'),
        ),
      ],
    );
  }
}

/// Game completion dialog
class GameCompletionDialog extends StatelessWidget {
  final Duration timeSpent;
  final VoidCallback? onPlayAgain;
  final VoidCallback? onBackToMenu;

  const GameCompletionDialog({
    super.key,
    required this.timeSpent,
    this.onPlayAgain,
    this.onBackToMenu,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.celebration,
            color: theme.colorScheme.primary,
            size: 28,
          ),
          const SizedBox(width: 8),
          const Text('Selamat!'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Anda telah menyelesaikan teka-teki silang ini!',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Text(
                  'Waktu yang dihabiskan:',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 4),
                Text(
                  _formatTime(timeSpent),
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: onBackToMenu,
          child: const Text('Kembali ke Menu'),
        ),
        FilledButton(
          onPressed: onPlayAgain,
          child: const Text('Main Lagi'),
        ),
      ],
    );
  }

  String _formatTime(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    if (hours > 0) {
      return '${hours}j ${minutes}m ${seconds}d';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}d';
    } else {
      return '${seconds}d';
    }
  }
}
