import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/router/app_router.dart';
import '../../providers/crossword_provider.dart';
import '../../widgets/crosswords/crossword_card.dart';

class FeaturedCrosswordsSection extends StatelessWidget {
  const FeaturedCrosswordsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '<PERSON>ka-<PERSON><PERSON>',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => AppRouter.goToCrosswords(context),
                  child: const Text('Lihat Semua'),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Consumer<CrosswordProvider>(
            builder: (context, provider, child) {
              if (provider.featuredCrosswords.isEmpty) {
                return Container(
                  height: 200,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  child: Card(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.grid_3x3,
                            size: 48,
                            color: theme.colorScheme.onSurface.withOpacity(0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            provider.isLoading
                                ? 'Memuat teka-teki unggulan...'
                                : 'Tidak ada teka-teki unggulan',
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color:
                                  theme.colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              return SizedBox(
                height: 280,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: provider.featuredCrosswords.length,
                  itemBuilder: (context, index) {
                    final crossword = provider.featuredCrosswords[index];
                    return Container(
                      width: 250,
                      margin: const EdgeInsets.only(right: 12),
                      child: CrosswordCard(
                        crossword: crossword,
                        onTap: () => AppRouter.goToCrosswordDetail(
                            context, crossword.id),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
