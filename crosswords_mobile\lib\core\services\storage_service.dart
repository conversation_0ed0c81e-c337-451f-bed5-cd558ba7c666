import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';

import '../config/app_config.dart';
import '../models/crossword_models.dart';

class StorageService {
  static late SharedPreferences _prefs;
  static late Box _box;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _box = await Hive.openBox('crosswords_cache');
  }

  // Token management
  Future<String?> getToken() async {
    return _prefs.getString(AppConfig.tokenKey);
  }

  Future<void> setToken(String token) async {
    await _prefs.setString(AppConfig.tokenKey, token);
  }

  Future<void> clearToken() async {
    await _prefs.remove(AppConfig.tokenKey);
  }

  // User data management
  Future<User?> getUser() async {
    final userJson = _prefs.getString(AppConfig.userKey);
    if (userJson != null) {
      try {
        final userMap = json.decode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      } catch (e) {
        // If parsing fails, remove corrupted data
        await clearUser();
        return null;
      }
    }
    return null;
  }

  Future<void> setUser(User user) async {
    final userJson = json.encode(user.toJson());
    await _prefs.setString(AppConfig.userKey, userJson);
  }

  Future<void> clearUser() async {
    await _prefs.remove(AppConfig.userKey);
  }

  // Theme management
  Future<String?> getThemeMode() async {
    return _prefs.getString(AppConfig.themeKey);
  }

  Future<void> setThemeMode(String themeMode) async {
    await _prefs.setString(AppConfig.themeKey, themeMode);
  }

  // Progress management
  Future<Progress?> getCrosswordProgress(String crosswordId) async {
    final progressJson = _prefs.getString('${AppConfig.progressKey}_$crosswordId');
    if (progressJson != null) {
      try {
        final progressMap = json.decode(progressJson) as Map<String, dynamic>;
        return Progress.fromJson(progressMap);
      } catch (e) {
        // If parsing fails, remove corrupted data
        await clearCrosswordProgress(crosswordId);
        return null;
      }
    }
    return null;
  }

  Future<void> setCrosswordProgress(String crosswordId, Progress progress) async {
    final progressJson = json.encode(progress.toJson());
    await _prefs.setString('${AppConfig.progressKey}_$crosswordId', progressJson);
  }

  Future<void> clearCrosswordProgress(String crosswordId) async {
    await _prefs.remove('${AppConfig.progressKey}_$crosswordId');
  }

  Future<List<String>> getAllProgressKeys() async {
    final keys = _prefs.getKeys();
    return keys
        .where((key) => key.startsWith(AppConfig.progressKey))
        .toList();
  }

  Future<void> clearAllProgress() async {
    final progressKeys = await getAllProgressKeys();
    for (final key in progressKeys) {
      await _prefs.remove(key);
    }
  }

  // Cache management using Hive
  Future<void> cacheData(String key, dynamic data) async {
    final cacheItem = CacheItem(
      data: data,
      timestamp: DateTime.now(),
    );
    await _box.put(key, cacheItem.toJson());
  }

  Future<T?> getCachedData<T>(
    String key,
    T Function(dynamic) fromJson, {
    Duration? maxAge,
  }) async {
    final cacheJson = _box.get(key);
    if (cacheJson != null) {
      try {
        final cacheItem = CacheItem.fromJson(cacheJson);
        
        // Check if cache is still valid
        final age = DateTime.now().difference(cacheItem.timestamp);
        final maxCacheAge = maxAge ?? AppConfig.cacheExpiry;
        
        if (age <= maxCacheAge) {
          return fromJson(cacheItem.data);
        } else {
          // Cache expired, remove it
          await _box.delete(key);
        }
      } catch (e) {
        // If parsing fails, remove corrupted data
        await _box.delete(key);
      }
    }
    return null;
  }

  Future<void> clearCache() async {
    await _box.clear();
  }

  Future<void> removeCachedData(String key) async {
    await _box.delete(key);
  }

  // Settings management
  Future<bool> getBoolSetting(String key, {bool defaultValue = false}) async {
    return _prefs.getBool(key) ?? defaultValue;
  }

  Future<void> setBoolSetting(String key, bool value) async {
    await _prefs.setBool(key, value);
  }

  Future<int> getIntSetting(String key, {int defaultValue = 0}) async {
    return _prefs.getInt(key) ?? defaultValue;
  }

  Future<void> setIntSetting(String key, int value) async {
    await _prefs.setInt(key, value);
  }

  Future<double> getDoubleSetting(String key, {double defaultValue = 0.0}) async {
    return _prefs.getDouble(key) ?? defaultValue;
  }

  Future<void> setDoubleSetting(String key, double value) async {
    await _prefs.setDouble(key, value);
  }

  Future<String> getStringSetting(String key, {String defaultValue = ''}) async {
    return _prefs.getString(key) ?? defaultValue;
  }

  Future<void> setStringSetting(String key, String value) async {
    await _prefs.setString(key, value);
  }

  // Game settings
  Future<double> getCellSize() async {
    return getDoubleSetting('cell_size', defaultValue: AppConfig.defaultCellSize);
  }

  Future<void> setCellSize(double size) async {
    await setDoubleSetting('cell_size', size);
  }

  Future<bool> getVibrationEnabled() async {
    return getBoolSetting('vibration_enabled', defaultValue: true);
  }

  Future<void> setVibrationEnabled(bool enabled) async {
    await setBoolSetting('vibration_enabled', enabled);
  }

  Future<bool> getAutoMoveEnabled() async {
    return getBoolSetting('auto_move_enabled', defaultValue: true);
  }

  Future<void> setAutoMoveEnabled(bool enabled) async {
    await setBoolSetting('auto_move_enabled', enabled);
  }

  Future<bool> getShowTimerEnabled() async {
    return getBoolSetting('show_timer_enabled', defaultValue: true);
  }

  Future<void> setShowTimerEnabled(bool enabled) async {
    await setBoolSetting('show_timer_enabled', enabled);
  }

  // Clear all data
  Future<void> clearAllData() async {
    await _prefs.clear();
    await _box.clear();
  }

  // Get storage info
  Future<Map<String, dynamic>> getStorageInfo() async {
    final keys = _prefs.getKeys();
    final cacheKeys = _box.keys;
    
    return {
      'preferences_count': keys.length,
      'cache_count': cacheKeys.length,
      'total_size_estimate': keys.length + cacheKeys.length,
    };
  }
}

// Cache item model
class CacheItem {
  final dynamic data;
  final DateTime timestamp;

  CacheItem({
    required this.data,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory CacheItem.fromJson(Map<String, dynamic> json) {
    return CacheItem(
      data: json['data'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}
