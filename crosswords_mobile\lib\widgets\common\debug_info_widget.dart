import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/config/app_config.dart';
import '../../providers/crossword_provider.dart';

/// Widget untuk menampilkan informasi debug
/// Berguna untuk troubleshooting masalah koneksi API
class DebugInfoWidget extends StatelessWidget {
  const DebugInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CrosswordProvider>(
      builder: (context, provider, child) {
        return Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.bug_report, color: Colors.orange),
                    const SizedBox(width: 8),
                    Text(
                      'Debug Info',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // API Configuration
                _buildInfoRow('API Base URL', AppConfig.baseUrl),
                _buildInfoRow('API Key', '${AppConfig.apiKey.substring(0, 8)}...'),
                
                const Divider(),
                
                // Provider State
                _buildInfoRow('Loading', provider.isLoading.toString()),
                _buildInfoRow('Error', provider.error ?? 'None'),
                _buildInfoRow('Crosswords Count', provider.crosswords.length.toString()),
                _buildInfoRow('Has More Data', provider.hasMoreData.toString()),
                
                const SizedBox(height: 16),
                
                // Action Buttons
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _testConnection(context),
                      icon: const Icon(Icons.wifi_find, size: 16),
                      label: const Text('Test API'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: () => _refreshData(context),
                      icon: const Icon(Icons.refresh, size: 16),
                      label: const Text('Refresh'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ],
      ),
    );
  }

  void _testConnection(BuildContext context) async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Testing koneksi API...'),
        duration: Duration(seconds: 2),
      ),
    );

    try {
      final provider = context.read<CrosswordProvider>();
      await provider.loadCrosswords(refresh: true);
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              provider.error != null 
                ? 'Koneksi gagal: ${provider.error}'
                : 'Koneksi berhasil! ${provider.crosswords.length} data ditemukan'
            ),
            backgroundColor: provider.error != null ? Colors.red : Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _refreshData(BuildContext context) {
    final provider = context.read<CrosswordProvider>();
    provider.loadCrosswords(refresh: true);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Memuat ulang data...'),
        duration: Duration(seconds: 1),
      ),
    );
  }
}
