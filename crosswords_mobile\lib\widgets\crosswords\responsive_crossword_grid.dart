import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/models/crossword_models.dart';
import '../../providers/crossword_game_provider.dart';
import 'crossword_grid.dart';

class ResponsiveCrosswordGrid extends StatelessWidget {
  final Crossword crossword;
  final bool enableZoom;
  final EdgeInsets? padding;

  const ResponsiveCrosswordGrid({
    super.key,
    required this.crossword,
    this.enableZoom = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate optimal cell size based on available space
        final availableWidth = constraints.maxWidth - (padding?.horizontal ?? 32);
        final availableHeight = constraints.maxHeight - (padding?.vertical ?? 32);
        
        // Calculate cell size to fit the grid
        final cellSizeFromWidth = availableWidth / crossword.gridSize;
        final cellSizeFromHeight = availableHeight / crossword.gridSize;
        
        // Use the smaller dimension to ensure grid fits
        final optimalCellSize = (cellSizeFromWidth < cellSizeFromHeight 
            ? cellSizeFromWidth 
            : cellSizeFromHeight).clamp(20.0, 60.0);

        return _ResponsiveGridContent(
          crossword: crossword,
          cellSize: optimalCellSize,
          enableZoom: enableZoom,
          padding: padding ?? const EdgeInsets.all(16),
          constraints: constraints,
        );
      },
    );
  }
}

class _ResponsiveGridContent extends StatefulWidget {
  final Crossword crossword;
  final double cellSize;
  final bool enableZoom;
  final EdgeInsets padding;
  final BoxConstraints constraints;

  const _ResponsiveGridContent({
    required this.crossword,
    required this.cellSize,
    required this.enableZoom,
    required this.padding,
    required this.constraints,
  });

  @override
  State<_ResponsiveGridContent> createState() => _ResponsiveGridContentState();
}

class _ResponsiveGridContentState extends State<_ResponsiveGridContent> {
  final TransformationController _transformationController = 
      TransformationController();

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final orientation = MediaQuery.of(context).orientation;
    final isLandscape = orientation == Orientation.landscape;
    
    // Adjust grid presentation based on orientation
    return Consumer<CrosswordGameProvider>(
      builder: (context, gameProvider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          child: _buildGridForOrientation(isLandscape, gameProvider),
        );
      },
    );
  }

  Widget _buildGridForOrientation(bool isLandscape, CrosswordGameProvider gameProvider) {
    if (isLandscape) {
      return _buildLandscapeGrid(gameProvider);
    } else {
      return _buildPortraitGrid(gameProvider);
    }
  }

  Widget _buildPortraitGrid(CrosswordGameProvider gameProvider) {
    return Center(
      child: SingleChildScrollView(
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: _buildInteractiveGrid(),
        ),
      ),
    );
  }

  Widget _buildLandscapeGrid(CrosswordGameProvider gameProvider) {
    // In landscape, we can afford to show the grid larger
    return Center(
      child: _buildInteractiveGrid(),
    );
  }

  Widget _buildInteractiveGrid() {
    final gridWidget = CrosswordGrid(
      crossword: widget.crossword,
      cellSize: widget.cellSize,
      enableZoom: false, // We handle zoom at this level
      padding: EdgeInsets.zero,
    );

    if (!widget.enableZoom) {
      return Container(
        padding: widget.padding,
        child: gridWidget,
      );
    }

    return InteractiveViewer(
      transformationController: _transformationController,
      boundaryMargin: const EdgeInsets.all(20),
      minScale: 0.5,
      maxScale: 3.0,
      panEnabled: true,
      scaleEnabled: true,
      constrained: false,
      child: Container(
        padding: widget.padding,
        child: gridWidget,
      ),
    );
  }
}

// Adaptive grid that adjusts to different screen sizes
class AdaptiveCrosswordGrid extends StatelessWidget {
  final Crossword crossword;
  final bool enableZoom;

  const AdaptiveCrosswordGrid({
    super.key,
    required this.crossword,
    this.enableZoom = true,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;
    final isTablet = screenWidth > 600;
    
    // Different layouts for different screen sizes
    if (isTablet) {
      return _buildTabletLayout();
    } else {
      return _buildPhoneLayout();
    }
  }

  Widget _buildTabletLayout() {
    return ResponsiveCrosswordGrid(
      crossword: crossword,
      enableZoom: enableZoom,
      padding: const EdgeInsets.all(24),
    );
  }

  Widget _buildPhoneLayout() {
    return ResponsiveCrosswordGrid(
      crossword: crossword,
      enableZoom: enableZoom,
      padding: const EdgeInsets.all(16),
    );
  }
}

// Grid with smart scaling based on content
class SmartScalingGrid extends StatefulWidget {
  final Crossword crossword;
  final bool autoScale;

  const SmartScalingGrid({
    super.key,
    required this.crossword,
    this.autoScale = true,
  });

  @override
  State<SmartScalingGrid> createState() => _SmartScalingGridState();
}

class _SmartScalingGridState extends State<SmartScalingGrid> {
  double _currentScale = 1.0;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (widget.autoScale) {
          _calculateOptimalScale(constraints);
        }

        return Transform.scale(
          scale: _currentScale,
          child: ResponsiveCrosswordGrid(
            crossword: widget.crossword,
            enableZoom: true,
          ),
        );
      },
    );
  }

  void _calculateOptimalScale(BoxConstraints constraints) {
    // Calculate scale based on grid size and available space
    final gridSize = widget.crossword.gridSize;
    final availableWidth = constraints.maxWidth;
    final availableHeight = constraints.maxHeight;
    
    // Estimate needed space for grid
    final estimatedGridWidth = gridSize * 40; // Assuming 40px per cell
    final estimatedGridHeight = gridSize * 40;
    
    final scaleX = availableWidth / estimatedGridWidth;
    final scaleY = availableHeight / estimatedGridHeight;
    
    final optimalScale = (scaleX < scaleY ? scaleX : scaleY).clamp(0.5, 2.0);
    
    if ((optimalScale - _currentScale).abs() > 0.1) {
      setState(() {
        _currentScale = optimalScale;
      });
    }
  }
}

// Utility class for grid calculations
class GridCalculations {
  static double calculateOptimalCellSize({
    required double availableWidth,
    required double availableHeight,
    required int gridSize,
    double minCellSize = 20.0,
    double maxCellSize = 60.0,
  }) {
    final cellSizeFromWidth = availableWidth / gridSize;
    final cellSizeFromHeight = availableHeight / gridSize;
    
    final optimalSize = cellSizeFromWidth < cellSizeFromHeight 
        ? cellSizeFromWidth 
        : cellSizeFromHeight;
        
    return optimalSize.clamp(minCellSize, maxCellSize);
  }

  static EdgeInsets calculateOptimalPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    
    if (screenWidth > 600) {
      return const EdgeInsets.all(24);
    } else {
      return const EdgeInsets.all(16);
    }
  }
}
