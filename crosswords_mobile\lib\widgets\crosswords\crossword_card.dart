import 'package:flutter/material.dart';

import '../../core/models/crossword_models.dart';

class CrosswordCard extends StatelessWidget {
  final Crossword crossword;
  final VoidCallback onTap;
  final bool showProgress;
  final double? progressValue;

  const CrosswordCard({
    super.key,
    required this.crossword,
    required this.onTap,
    this.showProgress = false,
    this.progressValue,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with difficulty and rating
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _DifficultyChip(difficulty: crossword.difficulty),
                  if (crossword.rating != null)
                    _RatingDisplay(
                      rating: crossword.rating!,
                      ratingCount: crossword.ratingCount ?? 0,
                    ),
                ],
              ),

              const SizedBox(height: 12),

              // Title
              Text(
                crossword.title,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 8),

              // Description
              if (crossword.description != null &&
                  crossword.description!.isNotEmpty)
                Text(
                  crossword.description!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

              const SizedBox(height: 12),

              // Progress bar (if applicable)
              if (showProgress && progressValue != null) ...[
                LinearProgressIndicator(
                  value: progressValue! / 100,
                  backgroundColor: theme.colorScheme.surfaceVariant,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${progressValue!.toInt()}% selesai',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
              ],

              // Footer with metadata
              Row(
                children: [
                  // Category
                  if (crossword.categoryName != null) ...[
                    Icon(
                      Icons.category,
                      size: 16,
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        crossword.categoryName!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],

                  // Grid size
                  Icon(
                    Icons.grid_3x3,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${crossword.gridSize}×${crossword.gridSize}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),

                  const Spacer(),

                  // Play count
                  Icon(
                    Icons.play_arrow,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${crossword.plays}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _DifficultyChip extends StatelessWidget {
  final String difficulty;

  const _DifficultyChip({required this.difficulty});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Color getColor() {
      switch (difficulty.toLowerCase()) {
        case 'mudah':
          return Colors.green;
        case 'sedang':
          return Colors.orange;
        case 'sulit':
          return Colors.red;
        default:
          return theme.colorScheme.primary;
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: getColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: getColor().withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        difficulty.toUpperCase(),
        style: theme.textTheme.bodySmall?.copyWith(
          color: getColor(),
          fontWeight: FontWeight.w600,
          fontSize: 10,
        ),
      ),
    );
  }
}

class _RatingDisplay extends StatelessWidget {
  final double rating;
  final int ratingCount;

  const _RatingDisplay({
    required this.rating,
    required this.ratingCount,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.star,
          size: 16,
          color: Colors.amber,
        ),
        const SizedBox(width: 4),
        Text(
          rating.toStringAsFixed(1),
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        if (ratingCount > 0) ...[
          const SizedBox(width: 4),
          Text(
            '($ratingCount)',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ],
    );
  }
}
