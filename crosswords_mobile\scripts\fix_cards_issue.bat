@echo off
echo ========================================
echo   Fix Cards Issue - Mobile App
echo ========================================
echo.

echo 1. Mendeteksi IP Address...
echo.
ipconfig | findstr "IPv4"
echo.

echo 2. Menampilkan konfigurasi saat ini...
echo.
type "lib\core\config\app_config.dart" | findstr "baseUrl"
echo.

echo 3. Testing koneksi ke server...
echo.
echo Pastikan server ber<PERSON><PERSON> dengan perintah:
echo   cd ..\
echo   php -S 0.0.0.0:1111 -t public
echo.

echo 4. Langkah selanjutnya:
echo   a. Ganti IP address di lib\core\config\app_config.dart
echo   b. Restart server dengan binding ke 0.0.0.0
echo   c. Jalankan aplikasi mobile
echo.

echo 5. Men<PERSON>lankan script Dart untuk deteksi otomatis...
dart run scripts/setup_api_config.dart

pause
