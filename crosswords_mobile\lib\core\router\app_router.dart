import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../screens/splash/splash_screen.dart';
import '../../screens/home/<USER>';
import '../../screens/auth/login_screen.dart';
import '../../screens/auth/register_screen.dart';
import '../../screens/crosswords/crosswords_list_screen.dart';
import '../../screens/crosswords/crossword_detail_screen.dart';
import '../../screens/crosswords/crossword_play_screen.dart';
import '../../screens/categories/categories_screen.dart';
import '../../screens/categories/category_detail_screen.dart';
import '../../screens/profile/profile_screen.dart';
import '../../screens/profile/edit_profile_screen.dart';
import '../../screens/progress/progress_screen.dart';
import '../../screens/settings/settings_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/splash',
    routes: [
      // Splash Screen
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Home Screen
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),

      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),

      // Crosswords Routes
      GoRoute(
        path: '/teka-teki-silang',
        name: 'crosswords',
        builder: (context, state) => const CrosswordsListScreen(),
      ),
      GoRoute(
        path: '/teka-teki-silang/:id',
        name: 'crossword-detail',
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          return CrosswordDetailScreen(crosswordId: id);
        },
      ),
      GoRoute(
        path: '/teka-teki-silang/:id/play',
        name: 'crossword-play',
        builder: (context, state) {
          final id = state.pathParameters['id']!;
          final resumeProgress = state.uri.queryParameters['resume'] == 'true';
          return CrosswordPlayScreen(
            crosswordId: id,
            resumeProgress: resumeProgress,
          );
        },
      ),

      // Categories Routes
      GoRoute(
        path: '/kategori',
        name: 'categories',
        builder: (context, state) => const CategoriesScreen(),
      ),
      GoRoute(
        path: '/kategori/:slug',
        name: 'category-detail',
        builder: (context, state) {
          final slug = state.pathParameters['slug']!;
          return CategoryDetailScreen(categorySlug: slug);
        },
      ),

      // Profile Routes
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: '/profile/edit',
        name: 'edit-profile',
        builder: (context, state) => const EditProfileScreen(),
      ),

      // Progress Screen
      GoRoute(
        path: '/progress',
        name: 'progress',
        builder: (context, state) => const ProgressScreen(),
      ),

      // Settings Screen
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Halaman Tidak Ditemukan'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'Halaman yang Anda cari tidak ditemukan',
              style: TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Kembali ke Beranda'),
            ),
          ],
        ),
      ),
    ),
  );

  // Navigation helpers
  static void goToHome(BuildContext context) {
    context.go('/');
  }

  static void goToLogin(BuildContext context) {
    context.go('/login');
  }

  static void goToRegister(BuildContext context) {
    context.go('/register');
  }

  static void goToCrosswords(BuildContext context) {
    context.go('/teka-teki-silang');
  }

  static void goToCrosswordDetail(BuildContext context, String id) {
    context.go('/teka-teki-silang/$id');
  }

  static void goToCrosswordPlay(BuildContext context, String id, {bool resume = false}) {
    final uri = Uri(
      path: '/teka-teki-silang/$id/play',
      queryParameters: resume ? {'resume': 'true'} : null,
    );
    context.go(uri.toString());
  }

  static void goToCategories(BuildContext context) {
    context.go('/kategori');
  }

  static void goToCategoryDetail(BuildContext context, String slug) {
    context.go('/kategori/$slug');
  }

  static void goToProfile(BuildContext context) {
    context.go('/profile');
  }

  static void goToEditProfile(BuildContext context) {
    context.go('/profile/edit');
  }

  static void goToProgress(BuildContext context) {
    context.go('/progress');
  }

  static void goToSettings(BuildContext context) {
    context.go('/settings');
  }

  // Push navigation (for modal-like behavior)
  static void pushLogin(BuildContext context) {
    context.push('/login');
  }

  static void pushRegister(BuildContext context) {
    context.push('/register');
  }

  static void pushCrosswordDetail(BuildContext context, String id) {
    context.push('/teka-teki-silang/$id');
  }

  static void pushCrosswordPlay(BuildContext context, String id, {bool resume = false}) {
    final uri = Uri(
      path: '/teka-teki-silang/$id/play',
      queryParameters: resume ? {'resume': 'true'} : null,
    );
    context.push(uri.toString());
  }

  static void pushCategoryDetail(BuildContext context, String slug) {
    context.push('/kategori/$slug');
  }

  static void pushEditProfile(BuildContext context) {
    context.push('/profile/edit');
  }

  static void pushSettings(BuildContext context) {
    context.push('/settings');
  }

  // Pop navigation
  static void pop(BuildContext context) {
    if (context.canPop()) {
      context.pop();
    } else {
      context.go('/');
    }
  }

  // Replace navigation (for authentication flows)
  static void replaceWithHome(BuildContext context) {
    context.go('/');
  }

  static void replaceWithLogin(BuildContext context) {
    context.go('/login');
  }
}
