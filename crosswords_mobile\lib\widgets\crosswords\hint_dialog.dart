import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/crossword_game_provider.dart';

class HintDialog extends StatefulWidget {
  const HintDialog({super.key});

  @override
  State<HintDialog> createState() => _HintDialogState();
}

class _HintDialogState extends State<HintDialog> {
  bool _isLoading = false;
  String? _hint;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadHint();
  }

  Future<void> _loadHint() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final gameProvider = context.read<CrosswordGameProvider>();
      final selectedCell = gameProvider.selectedCell;
      
      if (selectedCell == null) {
        setState(() {
          _error = 'Pilih sel terlebih dahulu untuk mendapatkan petunjuk';
          _isLoading = false;
        });
        return;
      }

      // Simulate hint generation (in real app, this would call an API)
      await Future.delayed(const Duration(milliseconds: 800));
      
      // Generate hint based on selected word
      final hint = _generateHint(selectedCell.clue);
      
      setState(() {
        _hint = hint;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Gagal memuat petunjuk: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  String _generateHint(String clue) {
    // Simple hint generation - in real app this would be more sophisticated
    final words = clue.split(' ');
    if (words.length > 3) {
      return 'Petunjuk: Fokus pada kata "${words[1]}" dalam clue ini.';
    } else if (clue.contains('nama')) {
      return 'Petunjuk: Ini adalah nama seseorang atau tempat.';
    } else if (clue.contains('warna')) {
      return 'Petunjuk: Ini adalah nama warna.';
    } else if (clue.contains('hewan')) {
      return 'Petunjuk: Ini adalah nama hewan.';
    } else {
      return 'Petunjuk: Coba pikirkan sinonim atau kata yang berhubungan dengan clue ini.';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          const Text('Petunjuk'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current clue
            Consumer<CrosswordGameProvider>(
              builder: (context, gameProvider, child) {
                final selectedCell = gameProvider.selectedCell;
                if (selectedCell != null) {
                  return Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${selectedCell.wordNumber} ${selectedCell.direction.name.toUpperCase()}',
                          style: theme.textTheme.labelMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          selectedCell.clue,
                          style: theme.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
            
            const SizedBox(height: 16),
            
            // Hint content
            if (_isLoading)
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 8),
                    Text('Memuat petunjuk...'),
                  ],
                ),
              )
            else if (_error != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: theme.colorScheme.onErrorContainer,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _error!,
                        style: TextStyle(
                          color: theme.colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            else if (_hint != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.tips_and_updates,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _hint!,
                        style: TextStyle(
                          color: theme.colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
      actions: [
        if (_error != null)
          TextButton(
            onPressed: _loadHint,
            child: const Text('Coba Lagi'),
          ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Tutup'),
        ),
        if (_hint != null)
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Could add logic to reveal a letter here
            },
            child: const Text('Terima Kasih'),
          ),
      ],
    );
  }
}
