import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../providers/crossword_game_provider.dart';
import '../../core/models/crossword_models.dart';

class PhysicalKeyboardHandler extends StatefulWidget {
  final Widget child;

  const PhysicalKeyboardHandler({
    super.key,
    required this.child,
  });

  @override
  State<PhysicalKeyboardHandler> createState() => _PhysicalKeyboardHandlerState();
}

class _PhysicalKeyboardHandlerState extends State<PhysicalKeyboardHandler> {
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Request focus when the widget is first built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    // Only handle key down events
    if (event is! KeyDownEvent) {
      return KeyEventResult.ignored;
    }

    final gameProvider = context.read<CrosswordGameProvider>();
    
    // Check if game is in playing state
    if (gameProvider.gameState != GameState.playing) {
      return KeyEventResult.ignored;
    }

    final key = event.logicalKey;
    
    // Handle letter input
    if (key.keyLabel.length == 1 && RegExp(r'[a-zA-Z]').hasMatch(key.keyLabel)) {
      final letter = key.keyLabel.toUpperCase();
      gameProvider.inputLetter(letter);
      return KeyEventResult.handled;
    }
    
    // Handle special keys
    switch (key) {
      case LogicalKeyboardKey.backspace:
        gameProvider.deleteLetter();
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.delete:
        gameProvider.clearCurrentCell();
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.space:
        gameProvider.moveToNextCell();
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.enter:
        gameProvider.moveToNextWord();
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.tab:
        if (HardwareKeyboard.instance.isShiftPressed) {
          gameProvider.moveToPreviousWord();
        } else {
          gameProvider.moveToNextWord();
        }
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.arrowUp:
        _handleArrowKey(gameProvider, Direction.up);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.arrowDown:
        _handleArrowKey(gameProvider, Direction.down);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.arrowLeft:
        _handleArrowKey(gameProvider, Direction.left);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.arrowRight:
        _handleArrowKey(gameProvider, Direction.right);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.escape:
        gameProvider.clearSelection();
        return KeyEventResult.handled;
        
      default:
        return KeyEventResult.ignored;
    }
  }

  void _handleArrowKey(CrosswordGameProvider gameProvider, Direction direction) {
    final selectedCell = gameProvider.selectedCell;
    if (selectedCell == null) return;

    int newRow = selectedCell.row;
    int newCol = selectedCell.col;

    switch (direction) {
      case Direction.up:
        newRow = (newRow - 1).clamp(0, gameProvider.crossword!.gridSize - 1);
        break;
      case Direction.down:
        newRow = (newRow + 1).clamp(0, gameProvider.crossword!.gridSize - 1);
        break;
      case Direction.left:
        newCol = (newCol - 1).clamp(0, gameProvider.crossword!.gridSize - 1);
        break;
      case Direction.right:
        newCol = (newCol + 1).clamp(0, gameProvider.crossword!.gridSize - 1);
        break;
      case Direction.across:
      case Direction.down:
        // These are not movement directions
        return;
    }

    // Check if the new position is a valid cell (not black/empty)
    final crossword = gameProvider.crossword!;
    final gridCell = crossword.gridData[newRow][newCol];
    
    if (!gridCell.isBlack && !gridCell.isEmpty) {
      gameProvider.selectCell(newRow, newCol);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      onKeyEvent: _handleKeyEvent,
      child: GestureDetector(
        onTap: () {
          // Ensure focus is maintained when tapping
          _focusNode.requestFocus();
        },
        behavior: HitTestBehavior.translucent,
        child: widget.child,
      ),
    );
  }
}

// Extension to add movement directions
enum Direction {
  up,
  down,
  left,
  right,
  across,
}

// Extension methods for CrosswordGameProvider to handle keyboard navigation
extension KeyboardNavigation on CrosswordGameProvider {
  void moveToNextCell() {
    final selectedCell = this.selectedCell;
    if (selectedCell == null || crossword == null) return;

    // Move to next cell in current word direction
    int newRow = selectedCell.row;
    int newCol = selectedCell.col;

    if (selectedCell.direction == Direction.across) {
      newCol++;
    } else {
      newRow++;
    }

    // Check bounds and if cell is valid
    if (newRow < crossword!.gridSize && newCol < crossword!.gridSize) {
      final gridCell = crossword!.gridData[newRow][newCol];
      if (!gridCell.isBlack && !gridCell.isEmpty) {
        selectCell(newRow, newCol);
        return;
      }
    }

    // If we can't move in current direction, try to move to next word
    moveToNextWord();
  }

  void moveToNextWord() {
    // Implementation would find the next word in the puzzle
    // For now, just maintain current selection
    notifyListeners();
  }

  void moveToPreviousWord() {
    // Implementation would find the previous word in the puzzle
    // For now, just maintain current selection
    notifyListeners();
  }

  void clearSelection() {
    // Clear current selection
    // Implementation depends on your game provider structure
    notifyListeners();
  }

  void clearCurrentCell() {
    final selectedCell = this.selectedCell;
    if (selectedCell == null) return;

    // Clear the current cell
    inputLetter('');
  }
}
