import 'dart:convert';

/// Script untuk debug alur API parsing
/// Jalankan dengan: dart run scripts/debug_api_flow.dart
void main() {
  print('🔍 Debug API Flow - Tracing JSON Parsing Issue');
  print('=' * 60);
  
  simulateApiFlow();
  
  print('\n✅ Debug selesai!');
}

void simulateApiFlow() {
  print('\n📡 Simulating API Response Flow...');
  
  // 1. Raw server response (seperti yang diberikan user)
  final serverResponse = {
    "status": "success",
    "data": [
      {
        "id": "dae3a1e6-b176-446d-a7ea-8e4c34abb1e5",
        "title": "Teka teki Edit",
        "slug": "teka-teki-edit",
        "description": "",
        "grid_size": 5,
        "difficulty": "mudah",
        "user_id": "5b4dc128-8ba5-4bd3-b990-6de4ddd11522",
        "is_public": 1,
        "plays": 15,
        "rating": null,
        "rating_count": 0,
        "category_id": "c001a2b4-6d7e-11ee-8c99-0242ac120002",
        "created_at": "2025-05-25T09:58:46.000000Z",
        "updated_at": "2025-05-25T11:20:05.000000Z",
        "category_name": "Pengetahuan Umum",
        "category_slug": "pengetahuan-umum",
        "creator": "widi yanata"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 12,
      "total": 13,
      "totalPages": 2
    }
  };
  
  print('\n🔍 Step 1: Raw Server Response');
  print('   Response type: ${serverResponse.runtimeType}');
  print('   Status: ${serverResponse['status']}');
  print('   Data type: ${serverResponse['data'].runtimeType}');
  print('   Data length: ${(serverResponse['data'] as List).length}');
  
  // 2. Simulate Dio response.data (ini yang diterima di api_service.dart)
  print('\n🔍 Step 2: Dio Response Processing');
  final dioResponseData = serverResponse; // Dio akan memberikan Map ini
  print('   Dio response.data type: ${dioResponseData.runtimeType}');
  print('   Is Map<String, dynamic>: ${dioResponseData is Map<String, dynamic>}');
  
  // 3. Simulate ApiResponse.fromJson call
  print('\n🔍 Step 3: ApiResponse.fromJson Processing');
  final jsonData = dioResponseData;
  final dataField = jsonData['data'];
  print('   json[\'data\'] type: ${dataField.runtimeType}');
  print('   json[\'data\'] is List: ${dataField is List}');
  
  // 4. Simulate fromJsonT function call (getCrosswords fromJson)
  print('\n🔍 Step 4: getCrosswords fromJson Function');
  try {
    // Ini adalah function yang dipanggil:
    // fromJson: (data) => (data as List).map((item) => Crossword.fromJson(item)).toList()
    
    print('   Input data type: ${dataField.runtimeType}');
    if (dataField is List) {
      print('   ✅ Data is List with ${dataField.length} items');
      print('   First item type: ${dataField.first.runtimeType}');
      print('   First item is Map: ${dataField.first is Map<String, dynamic>}');
      
      // Simulate Crossword.fromJson for first item
      final firstItem = dataField.first;
      print('   First item keys: ${(firstItem as Map).keys.toList()}');
      
      print('   ✅ This should work - List can be processed item by item');
    } else {
      print('   ❌ Data is not List: ${dataField.runtimeType}');
    }
  } catch (e) {
    print('   ❌ Error in fromJson: $e');
  }
  
  // 5. Identify the actual problem
  print('\n🔍 Step 5: Problem Analysis');
  print('   Expected flow:');
  print('   1. Server returns: {status, data: [...], pagination}');
  print('   2. Dio receives: Map<String, dynamic>');
  print('   3. ApiResponse.fromJson gets: Map<String, dynamic>');
  print('   4. fromJsonT gets: json[\'data\'] which is List');
  print('   5. fromJsonT processes: List -> List<Crossword>');
  print('');
  print('   🤔 If error still occurs, the issue might be:');
  print('   - Type casting problem in fromJsonT');
  print('   - Crossword.fromJson failing on individual items');
  print('   - Generic type mismatch');
  
  // 6. Test individual Crossword parsing
  print('\n🔍 Step 6: Test Individual Crossword Parsing');
  try {
    final firstCrosswordData = (serverResponse['data'] as List).first;
    print('   Testing Crossword.fromJson with first item...');
    print('   Item type: ${firstCrosswordData.runtimeType}');
    print('   Item keys: ${(firstCrosswordData as Map).keys.toList()}');
    
    // Simulate basic field access
    print('   ID: ${firstCrosswordData['id']}');
    print('   Title: ${firstCrosswordData['title']}');
    print('   Grid Size: ${firstCrosswordData['grid_size']}');
    
    print('   ✅ Individual item parsing should work');
  } catch (e) {
    print('   ❌ Individual item parsing failed: $e');
  }
}

/// Test the exact error scenario
void testErrorScenario() {
  print('\n🧪 Testing Error Scenario...');
  
  // Simulate the exact call that's failing
  final mockResponse = {
    "status": "success",
    "data": [
      {"id": "test", "title": "Test"}
    ]
  };
  
  try {
    // This simulates: ApiResponse.fromJson(response.data, fromJson)
    // where fromJson = (data) => (data as List).map(...).toList()
    
    final fromJsonFunction = (dynamic data) {
      print('   fromJson called with: ${data.runtimeType}');
      if (data is List) {
        return data.map((item) => item).toList(); // Simplified
      } else {
        throw Exception('Expected List but got ${data.runtimeType}');
      }
    };
    
    // Simulate ApiResponse.fromJson
    final dataField = mockResponse['data'];
    final result = fromJsonFunction(dataField);
    print('   ✅ Success: ${result.runtimeType}');
    
  } catch (e) {
    print('   ❌ Error: $e');
  }
}
