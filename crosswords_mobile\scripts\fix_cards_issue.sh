#!/bin/bash

echo "========================================"
echo "   Fix Cards Issue - Mobile App"
echo "========================================"
echo

echo "1. Mendeteksi IP Address..."
echo
if command -v ifconfig &> /dev/null; then
    ifconfig | grep "inet " | grep -v "127.0.0.1"
elif command -v ip &> /dev/null; then
    ip addr show | grep "inet " | grep -v "127.0.0.1"
else
    echo "Tidak dapat mendeteksi IP address secara otomatis"
fi
echo

echo "2. Menampilkan konfigurasi saat ini..."
echo
grep "baseUrl" lib/core/config/app_config.dart
echo

echo "3. Testing koneksi ke server..."
echo
echo "Pastikan server ber<PERSON><PERSON> dengan perintah:"
echo "  cd ../"
echo "  php -S 0.0.0.0:1111 -t public"
echo

echo "4. Langkah selanjutnya:"
echo "  a. Ganti IP address di lib/core/config/app_config.dart"
echo "  b. Restart server dengan binding ke 0.0.0.0"
echo "  c. Jalankan aplikasi mobile"
echo

echo "5. Menjalankan script Dart untuk deteksi otomatis..."
dart run scripts/setup_api_config.dart

echo
echo "Selesai!"
