import 'dart:convert';
import '../lib/core/models/crossword_models.dart';

/// Script untuk menguji parsing response server yang sebenarnya
/// Jalankan dengan: dart run scripts/test_real_response.dart
void main() {
  print('🧪 Testing Real Server Response Parsing');
  print('=' * 50);
  
  testRealResponseParsing();
  
  print('\n✅ Test selesai!');
}

void testRealResponseParsing() {
  print('\n🔍 Testing parsing response server yang sebenarnya...');
  
  // Response yang diberikan user
  final realResponse = {
    "status": "success",
    "data": [
      {
        "id": "dae3a1e6-b176-446d-a7ea-8e4c34abb1e5",
        "title": "Teka teki Edit",
        "slug": "teka-teki-edit",
        "description": "",
        "grid_size": 5,
        "grid_data": [
          [
            {"char": " ", "wordIds": []},
            {"char": " ", "wordIds": []},
            {"char": " ", "wordIds": []},
            {"char": " ", "wordIds": []},
            {"char": " ", "wordIds": []}
          ],
          [
            {"char": " ", "wordIds": []},
            {"char": "T", "wordIds": [2]},
            {"char": " ", "wordIds": []},
            {"char": " ", "wordIds": []},
            {"char": " ", "wordIds": []}
          ],
          [
            {"char": "T", "wordIds": [1]},
            {"char": "E", "wordIds": [1, 2]},
            {"char": "K", "wordIds": [1]},
            {"char": "A", "wordIds": [1]},
            {"char": " ", "wordIds": []}
          ],
          [
            {"char": " ", "wordIds": []},
            {"char": "K", "wordIds": [2]},
            {"char": " ", "wordIds": []},
            {"char": " ", "wordIds": []},
            {"char": " ", "wordIds": []}
          ],
          [
            {"char": " ", "wordIds": []},
            {"char": "I", "wordIds": [2, 3]},
            {"char": "O", "wordIds": [3]},
            {"char": " ", "wordIds": []},
            {"char": " ", "wordIds": []}
          ]
        ],
        "words": ["TEKA", "TEKI", "IO"],
        "clues": {
          "down": {"2": "teki"},
          "across": {"1": "teka", "3": "io"}
        },
        "word_positions": [
          {
            "col": 0,
            "row": 2,
            "word": "TEKA",
            "number": 1,
            "direction": "across"
          },
          {
            "col": 1,
            "row": 1,
            "word": "TEKI",
            "number": 2,
            "direction": "down"
          },
          {
            "col": 1,
            "row": 4,
            "word": "IO",
            "number": 3,
            "direction": "across"
          }
        ],
        "difficulty": "mudah",
        "user_id": "5b4dc128-8ba5-4bd3-b990-6de4ddd11522",
        "is_public": 1,
        "plays": 15,
        "rating": null,
        "rating_count": 0,
        "category_id": "c001a2b4-6d7e-11ee-8c99-0242ac120002",
        "created_at": "2025-05-25T09:58:46.000000Z",
        "updated_at": "2025-05-25T11:20:05.000000Z",
        "category_name": "Pengetahuan Umum",
        "category_slug": "pengetahuan-umum",
        "creator": "widi yanata"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 12,
      "total": 13,
      "totalPages": 2
    }
  };
  
  try {
    // Test 1: Parse response structure
    print('\n📋 Test 1: Response Structure');
    print('   Status: ${realResponse['status']}');
    print('   Data type: ${realResponse['data'].runtimeType}');
    print('   Data count: ${(realResponse['data'] as List).length}');
    print('   Pagination: ${realResponse['pagination']}');
    
    // Test 2: Parse crossword data
    print('\n📋 Test 2: Crossword Data Parsing');
    final crosswordData = (realResponse['data'] as List).first;
    final crossword = Crossword.fromJson(crosswordData);
    
    print('   ✅ ID: ${crossword.id}');
    print('   ✅ Title: ${crossword.title}');
    print('   ✅ Slug: ${crossword.slug}');
    print('   ✅ Grid Size: ${crossword.gridSize}');
    print('   ✅ Grid Data: ${crossword.gridData.length}x${crossword.gridData[0].length}');
    print('   ✅ Words: ${crossword.words}');
    print('   ✅ Difficulty: ${crossword.difficulty}');
    print('   ✅ Plays: ${crossword.plays}');
    print('   ✅ Rating: ${crossword.rating}');
    print('   ✅ Category: ${crossword.categoryName}');
    print('   ✅ Creator: ${crossword.creator}');
    
    // Test 3: Parse grid cells
    print('\n📋 Test 3: Grid Cell Parsing');
    for (int row = 0; row < crossword.gridData.length; row++) {
      String rowStr = '';
      for (int col = 0; col < crossword.gridData[row].length; col++) {
        final cell = crossword.gridData[row][col];
        rowStr += cell.char == ' ' ? '·' : cell.char;
      }
      print('   Row $row: $rowStr');
    }
    
    // Test 4: Parse clues
    print('\n📋 Test 4: Clues Parsing');
    print('   Across clues: ${crossword.clues.across}');
    print('   Down clues: ${crossword.clues.down}');
    
    // Test 5: Parse word positions
    print('\n📋 Test 5: Word Positions');
    for (final pos in crossword.wordPositions) {
      print('   ${pos.number}. ${pos.word} (${pos.direction}) at (${pos.row}, ${pos.col})');
    }
    
    // Test 6: Simulate API response parsing
    print('\n📋 Test 6: API Response Simulation');
    final List<Crossword> crosswords = (realResponse['data'] as List)
        .map((item) => Crossword.fromJson(item))
        .toList();
    
    print('   ✅ Successfully parsed ${crosswords.length} crosswords');
    print('   ✅ First crossword: ${crosswords.first.title}');
    
    print('\n🎉 Semua test berhasil! Response server dapat di-parse dengan benar.');
    
  } catch (e, stackTrace) {
    print('\n❌ Error parsing response: $e');
    print('Stack trace: $stackTrace');
  }
}

/// Test dengan berbagai skenario edge case
void testEdgeCases() {
  print('\n🔍 Testing edge cases...');
  
  // Test dengan data minimal
  final minimalData = {
    "id": "test-id",
    "title": "Test Crossword",
    "slug": "test-crossword",
    "grid_size": 3,
    "difficulty": "mudah",
    "user_id": "test-user",
    "is_public": 1,
    "plays": 0,
    "rating_count": 0,
    "created_at": "2025-01-01T00:00:00.000000Z",
    "updated_at": "2025-01-01T00:00:00.000000Z"
  };
  
  try {
    final crossword = Crossword.fromJson(minimalData);
    print('   ✅ Minimal data parsing: ${crossword.title}');
  } catch (e) {
    print('   ❌ Minimal data parsing failed: $e');
  }
  
  // Test dengan grid_data null
  final noGridData = {
    ...minimalData,
    "grid_data": null
  };
  
  try {
    final crossword = Crossword.fromJson(noGridData);
    print('   ✅ Null grid_data handling: ${crossword.gridData.length}x${crossword.gridData[0].length}');
  } catch (e) {
    print('   ❌ Null grid_data handling failed: $e');
  }
}
