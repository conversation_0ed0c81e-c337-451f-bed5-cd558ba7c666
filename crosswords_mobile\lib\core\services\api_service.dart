import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../config/app_config.dart';
import '../models/crossword_models.dart';
import 'storage_service.dart';

class ApiService {
  late final Dio _dio;
  final String baseUrl;
  final StorageService _storage = StorageService();

  ApiService(this.baseUrl) {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: AppConfig.connectionTimeout,
      receiveTimeout: AppConfig.apiTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': AppConfig.apiKey,
      },
    ));

    // Add interceptors
    _dio.interceptors.add(_AuthInterceptor(_storage));

    if (AppConfig.enableLogging && kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
      ));
    }
  }

  // Generic GET request
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
      );

      print('🔍 Raw response data type: ${response.data.runtimeType}');
      print('🔍 Response status: ${response.statusCode}');

      // Handle different response formats
      if (response.data is Map<String, dynamic>) {
        // Standard API response format
        final responseMap = response.data as Map<String, dynamic>;
        print('📊 Response status: ${responseMap['status']}');
        print('📈 Data field type: ${responseMap['data']?.runtimeType}');

        // Create ApiResponse manually to avoid parsing issues
        T? parsedData;
        if (responseMap['data'] != null && fromJson != null) {
          try {
            parsedData = fromJson(responseMap['data']);
            print('✅ Data parsing successful');
          } catch (e) {
            print('❌ Data parsing failed: $e');
            print('🔍 Raw data: ${responseMap['data']}');
            rethrow;
          }
        } else {
          parsedData = responseMap['data'] as T?;
        }

        return ApiResponse<T>(
          status: responseMap['status'] ?? 'success',
          message: responseMap['message'] ?? 'Data retrieved successfully',
          data: parsedData,
          meta: responseMap['meta'],
        );
      } else if (response.data is List) {
        // Direct array response - wrap in standard format (fallback)
        print('⚠️ Received direct array response, wrapping in standard format');
        return ApiResponse<T>(
          status: 'success',
          message: 'Data retrieved successfully',
          data:
              fromJson != null ? fromJson(response.data) : response.data as T?,
        );
      } else {
        // Unexpected format
        print('❌ Unexpected response format: ${response.data.runtimeType}');
        throw Exception(
            'Unexpected response format: ${response.data.runtimeType}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Generic POST request
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );

      // Handle different response formats
      if (response.data is Map<String, dynamic>) {
        return ApiResponse.fromJson(response.data, fromJson);
      } else if (response.data is List) {
        return ApiResponse<T>(
          status: 'success',
          message: 'Data retrieved successfully',
          data:
              fromJson != null ? fromJson(response.data) : response.data as T?,
        );
      } else {
        throw Exception(
            'Unexpected response format: ${response.data.runtimeType}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Generic PUT request
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );

      // Handle different response formats
      if (response.data is Map<String, dynamic>) {
        return ApiResponse.fromJson(response.data, fromJson);
      } else if (response.data is List) {
        return ApiResponse<T>(
          status: 'success',
          message: 'Data retrieved successfully',
          data:
              fromJson != null ? fromJson(response.data) : response.data as T?,
        );
      } else {
        throw Exception(
            'Unexpected response format: ${response.data.runtimeType}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Generic DELETE request
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        endpoint,
        queryParameters: queryParameters,
      );

      // Handle different response formats
      if (response.data is Map<String, dynamic>) {
        return ApiResponse.fromJson(response.data, fromJson);
      } else if (response.data is List) {
        return ApiResponse<T>(
          status: 'success',
          message: 'Data retrieved successfully',
          data:
              fromJson != null ? fromJson(response.data) : response.data as T?,
        );
      } else {
        throw Exception(
            'Unexpected response format: ${response.data.runtimeType}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Crossword endpoints
  Future<ApiResponse<List<Crossword>>> getCrosswords({
    String? category,
    String? difficulty,
    String? search,
    int page = 1,
    int limit = AppConfig.defaultPageSize,
  }) async {
    final queryParams = {
      if (category != null) 'category_id': category,
      if (difficulty != null) 'difficulty': difficulty,
      if (search != null) 'search': search,
      'page': page,
      'limit': limit,
    };

    print('🌐 API Request: GET /crosswords');
    print('📋 Query params: $queryParams');
    print('🔗 Base URL: ${AppConfig.baseUrl}');

    return get<List<Crossword>>(
      '/crosswords',
      queryParameters: queryParams,
      fromJson: (data) {
        print(
            '🔧 getCrosswords fromJson called with data type: ${data.runtimeType}');
        if (data is List) {
          print('🔧 Converting ${data.length} items to Crossword objects');
          return data.map((item) => Crossword.fromJson(item)).toList();
        } else {
          print('❌ Expected List but got ${data.runtimeType}');
          throw Exception('Expected List but got ${data.runtimeType}');
        }
      },
    );
  }

  Future<ApiResponse<Crossword>> getCrossword(String id) async {
    return get<Crossword>(
      '/crosswords/$id',
      fromJson: (data) => Crossword.fromJson(data),
    );
  }

  Future<ApiResponse<List<Crossword>>> getFeaturedCrosswords() async {
    return get<List<Crossword>>(
      '/featured',
      fromJson: (data) =>
          (data as List).map((item) => Crossword.fromJson(item)).toList(),
    );
  }

  Future<ApiResponse<void>> recordPlay(String crosswordId) async {
    return post<void>('/crosswords/$crosswordId/play');
  }

  // Category endpoints
  Future<ApiResponse<List<CrosswordCategory>>> getCategories() async {
    return get<List<CrosswordCategory>>(
      '/categories',
      fromJson: (data) => (data as List)
          .map((item) => CrosswordCategory.fromJson(item))
          .toList(),
    );
  }

  Future<ApiResponse<CrosswordCategory>> getCategory(String id) async {
    return get<CrosswordCategory>(
      '/categories/$id',
      fromJson: (data) => CrosswordCategory.fromJson(data),
    );
  }

  // Authentication endpoints
  Future<ApiResponse<Map<String, dynamic>>> login(
    String email,
    String password,
  ) async {
    return post<Map<String, dynamic>>(
      '/users/login',
      data: {
        'email': email,
        'password': password,
      },
      fromJson: (data) => Map<String, dynamic>.from(data),
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> register(
    String username,
    String email,
    String password,
    String? displayName,
  ) async {
    return post<Map<String, dynamic>>(
      '/users/register',
      data: {
        'username': username,
        'email': email,
        'password': password,
        if (displayName != null) 'display_name': displayName,
      },
      fromJson: (data) => Map<String, dynamic>.from(data),
    );
  }

  Future<ApiResponse<User>> getCurrentUser() async {
    return get<User>(
      '/users/me',
      fromJson: (data) => User.fromJson(data),
    );
  }

  Future<ApiResponse<User>> updateProfile(Map<String, dynamic> data) async {
    return put<User>(
      '/users/profile',
      data: data,
      fromJson: (data) => User.fromJson(data),
    );
  }

  Future<ApiResponse<void>> logout() async {
    return post<void>('/users/logout');
  }

  // Progress endpoints
  Future<ApiResponse<List<Progress>>> getProgress() async {
    return get<List<Progress>>(
      '/progress',
      fromJson: (data) =>
          (data as List).map((item) => Progress.fromJson(item)).toList(),
    );
  }

  Future<ApiResponse<Progress>> getCrosswordProgress(String crosswordId) async {
    return get<Progress>(
      '/progress/$crosswordId',
      fromJson: (data) => Progress.fromJson(data),
    );
  }

  Future<ApiResponse<Progress>> saveProgress(
    String crosswordId,
    Map<String, dynamic> progressData,
  ) async {
    return post<Progress>(
      '/progress/$crosswordId',
      data: progressData,
      fromJson: (data) => Progress.fromJson(data),
    );
  }

  Future<ApiResponse<void>> deleteProgress(String crosswordId) async {
    return delete<void>('/progress/$crosswordId');
  }

  // Rating endpoints
  Future<ApiResponse<void>> rateCrossword(
    String crosswordId,
    int rating, {
    String? review,
  }) async {
    return post<void>(
      '/ratings/crosswords/$crosswordId',
      data: {
        'rating': rating,
        if (review != null) 'review': review,
      },
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> getRatingStats(
    String crosswordId,
  ) async {
    return get<Map<String, dynamic>>(
      '/ratings/crosswords/$crosswordId/stats',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );
  }

  Future<ApiResponse<Rating>> getUserRating(String crosswordId) async {
    return get<Rating>(
      '/ratings/crosswords/$crosswordId/user',
      fromJson: (data) => Rating.fromJson(data),
    );
  }

  Future<ApiResponse<void>> deleteRating(String crosswordId) async {
    return delete<void>('/ratings/crosswords/$crosswordId');
  }

  Future<ApiResponse<List<Crossword>>> getTopRated({int limit = 10}) async {
    return get<List<Crossword>>(
      '/ratings/top-rated',
      queryParameters: {'limit': limit},
      fromJson: (data) =>
          (data as List).map((item) => Crossword.fromJson(item)).toList(),
    );
  }

  // Error handling
  Exception _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const ApiException('Koneksi timeout. Silakan coba lagi.');

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['message'] ?? 'Terjadi kesalahan';

        switch (statusCode) {
          case 401:
            return const ApiException(
                'Sesi telah berakhir. Silakan login kembali.');
          case 403:
            return const ApiException('Akses ditolak.');
          case 404:
            return const ApiException('Data tidak ditemukan.');
          case 422:
            return ApiException(message);
          case 500:
            return const ApiException('Terjadi kesalahan server.');
          default:
            return ApiException(message);
        }

      case DioExceptionType.cancel:
        return const ApiException('Permintaan dibatalkan.');

      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          return const ApiException('Tidak ada koneksi internet.');
        }
        return const ApiException('Terjadi kesalahan yang tidak diketahui.');

      default:
        return const ApiException('Terjadi kesalahan.');
    }
  }
}

// Auth Interceptor
class _AuthInterceptor extends Interceptor {
  final StorageService _storage;

  _AuthInterceptor(this._storage);

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await _storage.getToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      // Token expired, clear stored token
      _storage.clearToken();
    }
    handler.next(err);
  }
}

// Custom Exception
class ApiException implements Exception {
  final String message;

  const ApiException(this.message);

  @override
  String toString() => message;
}
