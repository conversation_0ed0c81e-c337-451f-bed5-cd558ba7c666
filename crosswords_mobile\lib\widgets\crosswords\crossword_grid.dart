import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/models/crossword_models.dart';
import '../../providers/crossword_game_provider.dart';
import 'crossword_cell.dart';
import 'keyboard_handler.dart';
import 'gesture_handler.dart';

class CrosswordGrid extends StatefulWidget {
  final Crossword crossword;
  final double? cellSize;
  final bool enableZoom;
  final EdgeInsets padding;

  const CrosswordGrid({
    super.key,
    required this.crossword,
    this.cellSize,
    this.enableZoom = true,
    this.padding = const EdgeInsets.all(16),
  });

  @override
  State<CrosswordGrid> createState() => _CrosswordGridState();
}

class _CrosswordGridState extends State<CrosswordGrid> {
  late TransformationController _transformationController;

  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
  }

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final gameProvider = context.watch<CrosswordGameProvider>();
    final screenSize = MediaQuery.of(context).size;

    // Calculate optimal cell size based on screen size
    final maxGridWidth = screenSize.width - widget.padding.horizontal;
    // Reserve space for clues and other UI elements
    final optimalCellSize =
        (maxGridWidth / widget.crossword.gridSize).clamp(30.0, 60.0);

    final cellSize = widget.cellSize ?? optimalCellSize;
    final gridSize = cellSize * widget.crossword.gridSize;

    Widget gridWidget = Container(
      width: gridSize,
      height: gridSize,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
          width: 2,
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.crossword.gridSize,
          childAspectRatio: 1.0,
        ),
        itemCount: widget.crossword.gridSize * widget.crossword.gridSize,
        itemBuilder: (context, index) {
          final row = index ~/ widget.crossword.gridSize;
          final col = index % widget.crossword.gridSize;
          final gridCell = widget.crossword.gridData[row][col];
          final userInput = gameProvider.userInput.isNotEmpty
              ? gameProvider.userInput[row][col]
              : '';

          return AnimatedCrosswordCell(
            row: row,
            col: col,
            gridCell: gridCell,
            userInput: userInput,
            cellSize: cellSize,
            onTap: () => _onCellTap(context, row, col),
          );
        },
      ),
    );

    // Wrap with zoom functionality if enabled
    if (widget.enableZoom) {
      gridWidget = InteractiveViewer(
        transformationController: _transformationController,
        boundaryMargin: const EdgeInsets.all(20),
        minScale: 0.5,
        maxScale: 3.0,
        panEnabled: true,
        scaleEnabled: true,
        onInteractionUpdate: (details) {
          // Handle zoom interaction if needed
        },
        child: gridWidget,
      );
    }

    return KeyboardHandler(
      child: GestureHandler(
        enableZoom: widget.enableZoom,
        enableSwipeNavigation: true,
        child: Container(
          padding: widget.padding,
          child: Center(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: gridWidget,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onCellTap(BuildContext context, int row, int col) {
    debugPrint('Grid _onCellTap called: ($row, $col)');

    final gameProvider = context.read<CrosswordGameProvider>();
    final gridCell = widget.crossword.gridData[row][col];

    debugPrint(
        'Cell state - isEmpty: ${gridCell.isEmpty}, isBlack: ${gridCell.isBlack}');
    debugPrint('Game state: ${gameProvider.gameState}');

    // Don't allow tapping on black or empty cells
    if (gridCell.isBlack || gridCell.isEmpty) {
      debugPrint('Tap ignored - cell is black or empty');
      return;
    }

    // Start game if not started
    if (gameProvider.gameState == GameState.idle) {
      debugPrint('Starting game from idle state');
      gameProvider.startGame();
    }

    // If game is paused, resume it
    if (gameProvider.gameState == GameState.paused) {
      debugPrint('Resuming game from paused state');
      gameProvider.startGame();
    }

    // Select the cell - this will handle word highlighting and clue display
    debugPrint('Selecting cell: ($row, $col)');
    gameProvider.selectCell(row, col);

    debugPrint(
        'Cell selection completed. Selected cell: ${gameProvider.selectedCell}');
  }
}

// Compact version of CrosswordGrid for previews
class CrosswordGridPreview extends StatelessWidget {
  final Crossword crossword;
  final double cellSize;
  final bool showUserInput;

  const CrosswordGridPreview({
    super.key,
    required this.crossword,
    this.cellSize = 20.0,
    this.showUserInput = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final gridSize = cellSize * crossword.gridSize;

    return Container(
      width: gridSize,
      height: gridSize,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(4),
      ),
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossword.gridSize,
          childAspectRatio: 1.0,
        ),
        itemCount: crossword.gridSize * crossword.gridSize,
        itemBuilder: (context, index) {
          final row = index ~/ crossword.gridSize;
          final col = index % crossword.gridSize;
          final gridCell = crossword.gridData[row][col];

          return Container(
            decoration: BoxDecoration(
              color: gridCell.isBlack
                  ? Colors.black87
                  : gridCell.isEmpty
                      ? Colors.black87
                      : Colors.white,
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
                width: 0.5,
              ),
            ),
            child: gridCell.isBlack
                ? null
                : Center(
                    child: Text(
                      showUserInput ? '' : gridCell.char.toUpperCase(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontSize: cellSize * 0.4,
                        fontWeight: FontWeight.bold,
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.3),
                      ),
                    ),
                  ),
          );
        },
      ),
    );
  }
}

// Responsive CrosswordGrid that adapts to screen size
class ResponsiveCrosswordGrid extends StatelessWidget {
  final Crossword crossword;
  final bool enableZoom;

  const ResponsiveCrosswordGrid({
    super.key,
    required this.crossword,
    this.enableZoom = true,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;

        // Calculate optimal cell size
        double cellSize;
        EdgeInsets padding;

        if (screenWidth < 600) {
          // Mobile layout
          cellSize = (screenWidth * 0.85) / crossword.gridSize;
          cellSize = cellSize.clamp(35.0, 55.0);
          padding = const EdgeInsets.all(8);
        } else {
          // Tablet/Desktop layout
          cellSize = (screenWidth * 0.6) / crossword.gridSize;
          cellSize = cellSize.clamp(35.0, 60.0);
          padding = const EdgeInsets.all(16);
        }

        return CrosswordGrid(
          crossword: crossword,
          cellSize: cellSize,
          enableZoom: enableZoom,
          padding: padding,
        );
      },
    );
  }
}
