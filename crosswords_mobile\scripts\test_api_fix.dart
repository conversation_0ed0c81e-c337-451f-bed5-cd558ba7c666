import 'dart:io';
import 'dart:convert';

/// Script untuk menguji perbaikan API parsing
/// Jalankan dengan: dart run scripts/test_api_fix.dart
void main() async {
  print('🧪 Testing API Fix untuk JSON Parsing');
  print('=' * 50);
  
  await testApiResponse();
  
  print('\n✅ Test selesai!');
}

Future<void> testApiResponse() async {
  print('\n🔍 Testing berbagai format response API...');
  
  // Test case 1: Standard API response format
  await testStandardFormat();
  
  // Test case 2: Direct array response
  await testDirectArrayFormat();
  
  // Test case 3: Error response
  await testErrorFormat();
}

Future<void> testStandardFormat() async {
  print('\n📋 Test 1: Standard API Response Format');
  
  final standardResponse = {
    'status': 'success',
    'message': 'Data retrieved successfully',
    'data': [
      {
        'id': 'test-1',
        'title': 'Test Crossword 1',
        'difficulty': 'mudah',
        'plays': 10,
        'rating': 4.5
      },
      {
        'id': 'test-2', 
        'title': 'Test Crossword 2',
        'difficulty': 'sedang',
        'plays': 25,
        'rating': 4.2
      }
    ],
    'pagination': {
      'page': 1,
      'limit': 12,
      'total': 2,
      'totalPages': 1
    }
  };
  
  print('   ✅ Format: Standard API response');
  print('   📊 Data count: ${(standardResponse['data'] as List).length}');
  print('   📄 Pagination: ${standardResponse['pagination']}');
}

Future<void> testDirectArrayFormat() async {
  print('\n📋 Test 2: Direct Array Response Format');
  
  final directArrayResponse = [
    {
      'id': 'test-1',
      'title': 'Test Crossword 1',
      'difficulty': 'mudah',
      'plays': 10,
      'rating': 4.5
    },
    {
      'id': 'test-2',
      'title': 'Test Crossword 2', 
      'difficulty': 'sedang',
      'plays': 25,
      'rating': 4.2
    }
  ];
  
  print('   ✅ Format: Direct array response');
  print('   📊 Data count: ${directArrayResponse.length}');
  print('   ⚠️  Note: Akan dibungkus dalam format standard oleh API service');
}

Future<void> testErrorFormat() async {
  print('\n📋 Test 3: Error Response Format');
  
  final errorResponse = {
    'status': 'error',
    'message': 'Data tidak ditemukan',
    'errors': {
      'category': 'Category tidak valid'
    }
  };
  
  print('   ❌ Format: Error response');
  print('   📝 Message: ${errorResponse['message']}');
  print('   🔍 Errors: ${errorResponse['errors']}');
}

/// Simulasi test koneksi ke API
Future<void> testApiConnection() async {
  print('\n🌐 Testing koneksi ke API...');
  
  final testUrls = [
    'http://localhost:1111/api/crosswords',
    'http://*************:1111/api/crosswords',
    'http://*************:1111/api/crosswords',
  ];
  
  for (final url in testUrls) {
    print('   Testing: $url');
    
    try {
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 3);
      
      final request = await client.getUrl(Uri.parse(url));
      final response = await request.close();
      
      if (response.statusCode == 200) {
        final responseBody = await response.transform(utf8.decoder).join();
        
        try {
          final jsonData = jsonDecode(responseBody);
          
          if (jsonData is Map<String, dynamic>) {
            print('   ✅ Response: Standard format');
            print('   📊 Status: ${jsonData['status']}');
            if (jsonData['data'] is List) {
              print('   📈 Data count: ${(jsonData['data'] as List).length}');
            }
          } else if (jsonData is List) {
            print('   ✅ Response: Direct array format');
            print('   📈 Data count: ${jsonData.length}');
          }
          
        } catch (e) {
          print('   ❌ Invalid JSON response');
        }
        
      } else {
        print('   ❌ HTTP ${response.statusCode}');
      }
      
      client.close();
      break; // Stop on first successful connection
      
    } catch (e) {
      print('   ❌ Connection failed: ${e.toString().split(':').first}');
    }
  }
}
