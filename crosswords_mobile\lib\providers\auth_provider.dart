import 'package:flutter/foundation.dart';

import '../core/models/crossword_models.dart';
import '../core/services/auth_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService;

  AuthProvider(this._authService) {
    _initializeAuth();
  }

  User? _user;
  bool _isLoading = true;
  bool _isAuthenticated = false;
  String? _error;

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  String? get error => _error;

  // Initialize authentication state
  Future<void> _initializeAuth() async {
    try {
      _isLoading = true;
      notifyListeners();

      _isAuthenticated = await _authService.isAuthenticated();
      
      if (_isAuthenticated) {
        _user = await _authService.getCurrentUser();
        
        // Try to refresh user data if token is expiring soon
        if (await _authService.isTokenExpiringSoon()) {
          await refreshUser();
        }
      }
    } catch (e) {
      _error = 'Gagal memuat data autentikasi';
      _isAuthenticated = false;
      _user = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final result = await _authService.login(email, password);
      
      if (result.isSuccess) {
        _user = result.user;
        _isAuthenticated = true;
        _error = null;
      } else {
        _error = result.message;
        _isAuthenticated = false;
        _user = null;
      }

      return result.isSuccess;
    } catch (e) {
      _error = 'Terjadi kesalahan saat login';
      _isAuthenticated = false;
      _user = null;
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Register
  Future<bool> register(
    String username,
    String email,
    String password,
    String? displayName,
  ) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final result = await _authService.register(
        username,
        email,
        password,
        displayName,
      );
      
      if (result.isSuccess) {
        _user = result.user;
        _isAuthenticated = true;
        _error = null;
      } else {
        _error = result.message;
        _isAuthenticated = false;
        _user = null;
      }

      return result.isSuccess;
    } catch (e) {
      _error = 'Terjadi kesalahan saat mendaftar';
      _isAuthenticated = false;
      _user = null;
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refresh user data
  Future<bool> refreshUser() async {
    try {
      if (!_isAuthenticated) return false;

      final result = await _authService.refreshUser();
      
      if (result.isSuccess) {
        _user = result.user;
        _error = null;
        notifyListeners();
        return true;
      } else {
        if (result.message.contains('Sesi telah berakhir') || 
            result.message.contains('Tidak terautentikasi')) {
          await logout();
        } else {
          _error = result.message;
          notifyListeners();
        }
        return false;
      }
    } catch (e) {
      _error = 'Gagal memperbarui data pengguna';
      notifyListeners();
      return false;
    }
  }

  // Update profile
  Future<bool> updateProfile(Map<String, dynamic> data) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final result = await _authService.updateProfile(data);
      
      if (result.isSuccess) {
        _user = result.user;
        _error = null;
      } else {
        _error = result.message;
      }

      return result.isSuccess;
    } catch (e) {
      _error = 'Terjadi kesalahan saat memperbarui profil';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      await _authService.logout();
    } catch (e) {
      // Ignore errors during logout
    } finally {
      _user = null;
      _isAuthenticated = false;
      _error = null;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Check if user has specific role or permission
  bool hasPermission(String permission) {
    // Implement permission checking logic if needed
    return _isAuthenticated;
  }

  // Get user display name
  String get displayName {
    if (_user == null) return 'Pengguna';
    return _user!.displayName ?? _user!.username;
  }

  // Get user avatar URL
  String? get avatarUrl => _user?.avatarUrl;

  // Get user email
  String? get email => _user?.email;

  // Get user username
  String? get username => _user?.username;

  // Auto-refresh token if needed
  Future<void> autoRefreshIfNeeded() async {
    if (_isAuthenticated) {
      await _authService.autoRefreshIfNeeded();
    }
  }
}
