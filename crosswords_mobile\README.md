# Crosswords Mobile - tekateki.io

Flutter mobile application for the tekateki.io crossword platform, replicating the functionality and design of the existing Next.js frontend.

## 🎯 Project Overview

This Flutter mobile app provides the same crossword gaming experience as the web version, optimized for mobile devices with touch interactions and responsive design.

## 🚀 Features

### Core Features
- ✅ Browse and view crossword puzzles (teka-teki-silang)
- ✅ Interactive crossword grid with touch controls
- ✅ Category browsing and filtering
- ✅ User authentication (login/register)
- ✅ User profile management
- ✅ Rating system for crosswords
- ✅ Progress tracking and pause/start functionality
- ✅ Indonesian language support as primary language

### Mobile-Specific Features
- 📱 Touch-optimized crossword grid
- 🎮 Haptic feedback for interactions
- 📐 Responsive layouts for different screen sizes
- 🔄 Pull-to-refresh functionality
- 💾 Offline support with local caching
- 🌙 Dark/Light theme support

### Excluded Features
- ❌ SEO optimization (not applicable for mobile)
- ❌ Crossword creation (users cannot create puzzles)
- ❌ Web-specific features

## 🏗️ Architecture

### Tech Stack
- **Framework**: Flutter 3.10+
- **State Management**: Provider
- **HTTP Client**: Dio
- **Local Storage**: Hive + SharedPreferences
- **Navigation**: GoRouter
- **UI**: Material Design 3

### Project Structure
```
lib/
├── core/
│   ├── config/          # App configuration
│   ├── models/          # Data models
│   ├── router/          # Navigation setup
│   ├── services/        # API and storage services
│   └── theme/           # App theming
├── providers/           # State management
├── screens/             # UI screens
├── widgets/             # Reusable widgets
└── main.dart           # App entry point
```

## 🛠️ Setup Instructions

### Prerequisites
- Flutter SDK 3.10.0 or higher
- Dart SDK 3.0.0 or higher
- Android Studio / VS Code
- Android SDK (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd crosswords-mobile
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure API endpoints**
   Edit `lib/core/config/app_config.dart`:
   ```dart
   static const String baseUrl = 'https://your-domain.com/tts-api/api';
   static const String apiKey = 'your-api-key-here';
   ```

4. **Run the app**
   ```bash
   # Debug mode
   flutter run
   
   # Release mode
   flutter run --release
   ```

### Build for Production

**Android APK:**
```bash
flutter build apk --release
```

**Android App Bundle:**
```bash
flutter build appbundle --release
```

**iOS:**
```bash
flutter build ios --release
```

## 🎨 Design System

### Color Scheme
The app uses a paper-inspired design matching the web version:

- **Light Theme**: White/off-white backgrounds with dark text
- **Dark Theme**: Dark backgrounds with light text
- **Accent Colors**: Blue, Green, Orange, Red for different UI elements

### Typography
- **Primary Font**: Roboto
- **Headings**: Bold weights for emphasis
- **Body Text**: Regular weights for readability

## 🔧 Configuration

### API Configuration
Update the following in `app_config.dart`:
- `baseUrl`: Your API server URL
- `apiKey`: Your API key for authentication

### App Settings
- Default grid size: 15x15
- Cell size range: 30-60px
- Cache expiry: 24 hours
- Page size: 20 items

## 📱 Mobile Optimizations

### Touch Interactions
- Tap to select cells
- Swipe gestures for navigation
- Pinch-to-zoom for grid scaling
- Haptic feedback for actions

### Performance
- Image caching for better loading
- Lazy loading for lists
- Background data sync
- Memory-efficient grid rendering

### Responsive Design
- Adaptive layouts for different screen sizes
- Portrait/landscape orientation support
- Safe area handling
- Keyboard-aware scrolling

## 🔌 API Integration

The app connects to the same PHP API backend as the web version:

### Endpoints Used
- `/api/crosswords` - Get crosswords list
- `/api/crosswords/{id}` - Get crossword details
- `/api/categories` - Get categories
- `/api/users/login` - User authentication
- `/api/progress` - Save/load game progress
- `/api/ratings` - Rating system

### Authentication
- JWT token-based authentication
- Automatic token refresh
- Secure storage of credentials

## 🗄️ Data Management

### Local Storage
- **Hive**: Complex data caching
- **SharedPreferences**: Simple key-value storage
- **Progress Sync**: Local-first with server backup

### Caching Strategy
- Featured crosswords: 1 hour
- Categories: 6 hours
- Crossword details: 2 hours
- User progress: Real-time sync

## 🧪 Testing

### Running Tests
```bash
# Unit tests
flutter test

# Integration tests
flutter test integration_test/

# Widget tests
flutter test test/widget_test.dart
```

### Test Coverage
- Unit tests for business logic
- Widget tests for UI components
- Integration tests for user flows

## 📦 Dependencies

### Core Dependencies
- `provider`: State management
- `dio`: HTTP client
- `hive`: Local database
- `go_router`: Navigation
- `google_fonts`: Typography

### UI Dependencies
- `cached_network_image`: Image caching
- `flutter_svg`: SVG support
- `lottie`: Animations

### Utility Dependencies
- `shared_preferences`: Simple storage
- `jwt_decoder`: Token handling
- `vibration`: Haptic feedback

## 🚀 Deployment

### Android Play Store
1. Build signed APK/AAB
2. Upload to Play Console
3. Configure store listing
4. Submit for review

### iOS App Store
1. Build for iOS
2. Upload to App Store Connect
3. Configure app metadata
4. Submit for review

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🔄 Version History

- **v1.0.0**: Initial release with core features
- **v1.1.0**: Enhanced mobile optimizations (planned)
- **v1.2.0**: Additional game features (planned)

---

**Built with ❤️ for the tekateki.io community**
