import 'package:equatable/equatable.dart';

// Base API Response Model
class ApiResponse<T> extends Equatable {
  final String status;
  final String message;
  final T? data;
  final Map<String, dynamic>? meta;

  const ApiResponse({
    required this.status,
    required this.message,
    this.data,
    this.meta,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    T? parsedData;

    if (json['data'] != null && fromJsonT != null) {
      try {
        parsedData = fromJsonT(json['data']);
      } catch (e) {
        print('❌ Error parsing data in ApiResponse: $e');
        print('🔍 Data type: ${json['data'].runtimeType}');
        print('🔍 Data content: ${json['data']}');
        // Fallback to raw data if parsing fails
        parsedData = json['data'] as T?;
      }
    } else {
      parsedData = json['data'] as T?;
    }

    return ApiResponse<T>(
      status: json['status'] ?? '',
      message: json['message'] ?? '',
      data: parsedData,
      meta: json['meta'],
    );
  }

  bool get isSuccess => status == 'success';

  @override
  List<Object?> get props => [status, message, data, meta];
}

// User Model
class User extends Equatable {
  final String id;
  final String username;
  final String email;
  final String? displayName;
  final String? bio;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  const User({
    required this.id,
    required this.username,
    required this.email,
    this.displayName,
    this.bio,
    this.avatarUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'].toString(),
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      displayName: json['display_name'],
      bio: json['bio'],
      avatarUrl: json['avatar_url'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'display_name': displayName,
      'bio': bio,
      'avatar_url': avatarUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props =>
      [id, username, email, displayName, bio, avatarUrl, createdAt, updatedAt];
}

// Category Model
class CrosswordCategory extends Equatable {
  final String id;
  final String name;
  final String slug;
  final String? description;
  final String? iconUrl;
  final int crosswordCount;

  const CrosswordCategory({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.iconUrl,
    required this.crosswordCount,
  });

  factory CrosswordCategory.fromJson(Map<String, dynamic> json) {
    return CrosswordCategory(
      id: json['id'].toString(),
      name: json['name'] ?? '',
      slug: json['slug'] ?? '',
      description: json['description'],
      iconUrl: json['icon_url'],
      crosswordCount: json['crossword_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'icon_url': iconUrl,
      'crossword_count': crosswordCount,
    };
  }

  @override
  List<Object?> get props =>
      [id, name, slug, description, iconUrl, crosswordCount];
}

// Grid Cell Model
class GridCell extends Equatable {
  final String char;
  final List<int> wordIds;

  const GridCell({
    required this.char,
    required this.wordIds,
  });

  factory GridCell.fromJson(Map<String, dynamic> json) {
    // Handle wordIds with safe type conversion
    List<int> wordIds = [];
    if (json['wordIds'] != null) {
      try {
        final rawWordIds = json['wordIds'];
        if (rawWordIds is List) {
          wordIds = rawWordIds
              .map((e) => e is int ? e : int.tryParse(e.toString()) ?? 0)
              .toList();
        }
      } catch (e) {
        print('⚠️ Error parsing wordIds in GridCell: $e');
        wordIds = [];
      }
    }

    return GridCell(
      char: json['char']?.toString() ?? '',
      wordIds: wordIds,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'char': char,
      'wordIds': wordIds,
    };
  }

  bool get isEmpty => char.trim().isEmpty;
  bool get isBlack => char == '#' || char == '*';

  @override
  List<Object?> get props => [char, wordIds];
}

// Word Position Model
class WordPosition extends Equatable {
  final String word;
  final int row;
  final int col;
  final String direction; // 'across' or 'down'
  final int? number;

  const WordPosition({
    required this.word,
    required this.row,
    required this.col,
    required this.direction,
    this.number,
  });

  factory WordPosition.fromJson(Map<String, dynamic> json) {
    return WordPosition(
      word: json['word'] ?? '',
      row: json['row'] ?? 0,
      col: json['col'] ?? 0,
      direction: json['direction'] ?? 'across',
      number: json['number'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'word': word,
      'row': row,
      'col': col,
      'direction': direction,
      'number': number,
    };
  }

  bool get isAcross => direction == 'across';
  bool get isDown => direction == 'down';

  @override
  List<Object?> get props => [word, row, col, direction, number];
}

// Crossword Clues Model
class CrosswordClues extends Equatable {
  final Map<String, String> across;
  final Map<String, String> down;

  const CrosswordClues({
    required this.across,
    required this.down,
  });

  factory CrosswordClues.fromJson(Map<String, dynamic> json) {
    return CrosswordClues(
      across: Map<String, String>.from(json['across'] ?? {}),
      down: Map<String, String>.from(json['down'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'across': across,
      'down': down,
    };
  }

  @override
  List<Object?> get props => [across, down];
}

// Crossword Model
class Crossword extends Equatable {
  final String id;
  final String title;
  final String slug;
  final String? description;
  final int gridSize;
  final List<List<GridCell>> gridData;
  final List<String> words;
  final CrosswordClues clues;
  final List<WordPosition> wordPositions;
  final String difficulty;
  final String? userId;
  final bool isPublic;
  final int plays;
  final double? rating;
  final int? ratingCount;
  final String? categoryId;
  final String? categoryName;
  final String? categorySlug;
  final String? creator;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Crossword({
    required this.id,
    required this.title,
    required this.slug,
    this.description,
    required this.gridSize,
    required this.gridData,
    required this.words,
    required this.clues,
    required this.wordPositions,
    required this.difficulty,
    this.userId,
    required this.isPublic,
    required this.plays,
    this.rating,
    this.ratingCount,
    this.categoryId,
    this.categoryName,
    this.categorySlug,
    this.creator,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Crossword.fromJson(Map<String, dynamic> json) {
    final gridSize = json['grid_size'] ?? 15;

    // Handle grid_data which can be either string[][] or GridCell[][]
    List<List<GridCell>> gridData = [];
    if (json['grid_data'] != null) {
      try {
        final rawGrid = json['grid_data'] as List;
        for (var row in rawGrid) {
          List<GridCell> gridRow = [];
          if (row is List) {
            for (var cell in row) {
              try {
                if (cell is String) {
                  // Convert string to GridCell
                  gridRow.add(GridCell(char: cell, wordIds: const []));
                } else if (cell is Map) {
                  // Convert Map to Map<String, dynamic> safely
                  final cellMap = Map<String, dynamic>.from(cell);
                  gridRow.add(GridCell.fromJson(cellMap));
                } else {
                  // Fallback for unexpected cell type
                  gridRow.add(const GridCell(char: ' ', wordIds: []));
                }
              } catch (e) {
                print('⚠️ Error parsing grid cell: $e');
                gridRow.add(const GridCell(char: ' ', wordIds: []));
              }
            }
          }
          gridData.add(gridRow);
        }
      } catch (e) {
        print('⚠️ Error parsing grid_data: $e');
        // Create empty grid as fallback
        gridData = List.generate(
          gridSize,
          (row) => List.generate(
            gridSize,
            (col) => const GridCell(char: ' ', wordIds: []),
          ),
        );
      }
    } else {
      // Create empty grid if no grid_data provided (for featured crosswords)
      gridData = List.generate(
        gridSize,
        (row) => List.generate(
          gridSize,
          (col) => const GridCell(char: ' ', wordIds: []),
        ),
      );
    }

    return Crossword(
      id: json['id'].toString(),
      title: json['title'] ?? '',
      slug: json['slug'] ?? '',
      description: json['description'],
      gridSize: json['grid_size'] ?? 15,
      gridData: gridData,
      words: _parseStringList(json['words']),
      clues: _parseClues(json['clues']),
      wordPositions: _parseWordPositions(json['word_positions']),
      difficulty: json['difficulty'] ?? 'sedang',
      userId: json['user_id']?.toString(),
      isPublic: json['is_public'] == 1 || json['is_public'] == true,
      plays: json['plays'] ?? 0,
      rating: json['rating']?.toDouble(),
      ratingCount: json['rating_count'],
      categoryId: json['category_id']?.toString(),
      categoryName: json['category_name'],
      categorySlug: json['category_slug'],
      creator: json['creator'],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : DateTime.now(),
    );
  }

  // Helper methods for safe parsing
  static List<String> _parseStringList(dynamic data) {
    try {
      if (data is List) {
        return data.map((e) => e?.toString() ?? '').toList();
      }
      return [];
    } catch (e) {
      print('⚠️ Error parsing string list: $e');
      return [];
    }
  }

  static CrosswordClues _parseClues(dynamic data) {
    try {
      if (data is Map) {
        return CrosswordClues.fromJson(Map<String, dynamic>.from(data));
      }
      return const CrosswordClues(across: {}, down: {});
    } catch (e) {
      print('⚠️ Error parsing clues: $e');
      return const CrosswordClues(across: {}, down: {});
    }
  }

  static List<WordPosition> _parseWordPositions(dynamic data) {
    try {
      if (data is List) {
        return data
            .map((e) {
              try {
                if (e is Map) {
                  return WordPosition.fromJson(Map<String, dynamic>.from(e));
                }
                return null;
              } catch (e) {
                print('⚠️ Error parsing word position: $e');
                return null;
              }
            })
            .where((e) => e != null)
            .cast<WordPosition>()
            .toList();
      }
      return [];
    } catch (e) {
      print('⚠️ Error parsing word positions: $e');
      return [];
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'slug': slug,
      'description': description,
      'grid_size': gridSize,
      'grid_data': gridData
          .map((row) => row.map((cell) => cell.toJson()).toList())
          .toList(),
      'words': words,
      'clues': clues.toJson(),
      'word_positions': wordPositions.map((wp) => wp.toJson()).toList(),
      'difficulty': difficulty,
      'user_id': userId,
      'is_public': isPublic,
      'plays': plays,
      'rating': rating,
      'rating_count': ratingCount,
      'category_id': categoryId,
      'category_name': categoryName,
      'category_slug': categorySlug,
      'creator': creator,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        title,
        slug,
        description,
        gridSize,
        gridData,
        words,
        clues,
        wordPositions,
        difficulty,
        userId,
        isPublic,
        plays,
        rating,
        ratingCount,
        categoryId,
        categoryName,
        categorySlug,
        creator,
        createdAt,
        updatedAt
      ];
}

// Progress Model
class Progress extends Equatable {
  final String id;
  final String crosswordId;
  final String userId;
  final List<List<String>> userInput;
  final int completedCells;
  final int totalCells;
  final Duration timeSpent;
  final bool isCompleted;
  final DateTime lastPlayed;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Progress({
    required this.id,
    required this.crosswordId,
    required this.userId,
    required this.userInput,
    required this.completedCells,
    required this.totalCells,
    required this.timeSpent,
    required this.isCompleted,
    required this.lastPlayed,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Progress.fromJson(Map<String, dynamic> json) {
    return Progress(
      id: json['id'].toString(),
      crosswordId: json['crossword_id'].toString(),
      userId: json['user_id'].toString(),
      userInput: (json['user_input'] as List)
          .map((row) => List<String>.from(row))
          .toList(),
      completedCells: json['completed_cells'] ?? 0,
      totalCells: json['total_cells'] ?? 0,
      timeSpent: Duration(seconds: json['time_spent'] ?? 0),
      isCompleted: json['is_completed'] == 1 || json['is_completed'] == true,
      lastPlayed: DateTime.parse(json['last_played']),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'crossword_id': crosswordId,
      'user_id': userId,
      'user_input': userInput,
      'completed_cells': completedCells,
      'total_cells': totalCells,
      'time_spent': timeSpent.inSeconds,
      'is_completed': isCompleted,
      'last_played': lastPlayed.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  double get progressPercentage =>
      totalCells > 0 ? (completedCells / totalCells) * 100 : 0.0;

  @override
  List<Object?> get props => [
        id,
        crosswordId,
        userId,
        userInput,
        completedCells,
        totalCells,
        timeSpent,
        isCompleted,
        lastPlayed,
        createdAt,
        updatedAt
      ];
}

// Rating Model
class Rating extends Equatable {
  final String id;
  final String crosswordId;
  final String userId;
  final int rating;
  final String? review;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Rating({
    required this.id,
    required this.crosswordId,
    required this.userId,
    required this.rating,
    this.review,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Rating.fromJson(Map<String, dynamic> json) {
    return Rating(
      id: json['id'].toString(),
      crosswordId: json['crossword_id'].toString(),
      userId: json['user_id'].toString(),
      rating: json['rating'] ?? 0,
      review: json['review'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'crossword_id': crosswordId,
      'user_id': userId,
      'rating': rating,
      'review': review,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props =>
      [id, crosswordId, userId, rating, review, createdAt, updatedAt];
}
