import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/router/app_router.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authProvider = context.watch<AuthProvider>();
    final themeProvider = context.watch<ThemeProvider>();

    return Drawer(
      child: Column(
        children: [
          // Header
          Container(
            height: 200,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.primary.withOpacity(0.8),
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: <PERSON>umn(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // App Logo
                    Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.onPrimary.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.grid_3x3,
                            color: theme.colorScheme.onPrimary,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'tekateki.io',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            color: theme.colorScheme.onPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    
                    const Spacer(),
                    
                    // User Info
                    if (authProvider.isAuthenticated) ...[
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 24,
                            backgroundImage: authProvider.avatarUrl != null
                                ? NetworkImage(authProvider.avatarUrl!)
                                : null,
                            child: authProvider.avatarUrl == null
                                ? Text(
                                    authProvider.displayName[0].toUpperCase(),
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )
                                : null,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  authProvider.displayName,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: theme.colorScheme.onPrimary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                if (authProvider.email != null)
                                  Text(
                                    authProvider.email!,
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.onPrimary.withOpacity(0.8),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ] else ...[
                      Text(
                        'Selamat Datang!',
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Masuk untuk menyimpan progress',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onPrimary.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),

          // Menu Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _DrawerItem(
                  icon: Icons.home,
                  title: 'Beranda',
                  onTap: () {
                    Navigator.pop(context);
                    AppRouter.goToHome(context);
                  },
                ),
                
                _DrawerItem(
                  icon: Icons.grid_3x3,
                  title: 'Teka-Teki Silang',
                  onTap: () {
                    Navigator.pop(context);
                    AppRouter.goToCrosswords(context);
                  },
                ),
                
                _DrawerItem(
                  icon: Icons.category,
                  title: 'Kategori',
                  onTap: () {
                    Navigator.pop(context);
                    AppRouter.goToCategories(context);
                  },
                ),

                if (authProvider.isAuthenticated) ...[
                  const Divider(),
                  
                  _DrawerItem(
                    icon: Icons.person,
                    title: 'Profil',
                    onTap: () {
                      Navigator.pop(context);
                      AppRouter.goToProfile(context);
                    },
                  ),
                  
                  _DrawerItem(
                    icon: Icons.timeline,
                    title: 'Progress',
                    onTap: () {
                      Navigator.pop(context);
                      AppRouter.goToProgress(context);
                    },
                  ),
                ],

                const Divider(),
                
                _DrawerItem(
                  icon: themeProvider.currentThemeIcon,
                  title: 'Tema: ${themeProvider.currentThemeName}',
                  onTap: () async {
                    await themeProvider.toggleTheme();
                  },
                ),
                
                _DrawerItem(
                  icon: Icons.settings,
                  title: 'Pengaturan',
                  onTap: () {
                    Navigator.pop(context);
                    AppRouter.goToSettings(context);
                  },
                ),

                if (!authProvider.isAuthenticated) ...[
                  const Divider(),
                  
                  _DrawerItem(
                    icon: Icons.login,
                    title: 'Masuk',
                    onTap: () {
                      Navigator.pop(context);
                      AppRouter.goToLogin(context);
                    },
                  ),
                  
                  _DrawerItem(
                    icon: Icons.person_add,
                    title: 'Daftar',
                    onTap: () {
                      Navigator.pop(context);
                      AppRouter.goToRegister(context);
                    },
                  ),
                ] else ...[
                  const Divider(),
                  
                  _DrawerItem(
                    icon: Icons.logout,
                    title: 'Keluar',
                    onTap: () async {
                      Navigator.pop(context);
                      await authProvider.logout();
                      AppRouter.replaceWithHome(context);
                    },
                  ),
                ],
              ],
            ),
          ),

          // Footer
          Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              'tekateki.io v1.0.0',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

class _DrawerItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;

  const _DrawerItem({
    required this.icon,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: theme.colorScheme.onSurface.withOpacity(0.7),
      ),
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 4),
    );
  }
}
